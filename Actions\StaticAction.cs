﻿using System;
using System.Diagnostics;
using System.Windows.Automation;

namespace DesktopRpaLib.Actions
{
    class StaticAction
    {


        // 获取静态文本内容
        public string GetStaticText(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var staticText = element.GetCurrentPropertyValue(AutomationElement.NameProperty)?.ToString();
            if (string.IsNullOrEmpty(staticText))
            {
                throw new Exception("静态文本为空或未设置");
            }

            return staticText;
        }




    }
}
