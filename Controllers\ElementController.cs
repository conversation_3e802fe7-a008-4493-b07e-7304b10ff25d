﻿using System.Web.Http;
using DesktopRpaLib.Response;
using DesktopRpaLib.Actions;
using System;



[RoutePrefix("api/element/{ProcessId}")] // 定义路由前缀
public class ElementController : ApiController
{

    private readonly ElementAction _elementAction = new ElementAction();




    // 根据元素名称判断该元素是否存在
    [Route("isExistByName")]
    public IHttpActionResult GetIsExistByName(int ProcessId, [FromUri] string elementName)
    {

        if (string.IsNullOrEmpty(elementName))
        {
            return Json(ResponseResult<string>.Error(msg: "elementName参数不能为空"));
        }


        try
        {
            var exists = _elementAction.isExistByName(ProcessId, elementName);
            return Json(ResponseResult<object>.Success(
                msg: exists ? "元素存在" : "元素不存在",
                data: new { ProcessId, ElementName = elementName, Exists = exists }
            ));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }



    // 根据元素的 AutomationId 判断该元素是否存在
    [Route("isExistByAutomationId")]
    public IHttpActionResult GetIsExistByAutomationId(int ProcessId, [FromUri] string automationId)
    {
        if (string.IsNullOrEmpty(automationId))
        {
            return Json(ResponseResult<string>.Error(msg: "automationId参数不能为空"));
        }



        try
        {
            var exists = _elementAction.isExistByAutomationId(ProcessId, automationId);
            return Json(ResponseResult<object>.Success(
                msg: exists ? "元素存在" : "元素不存在",
                data: new { ProcessId, AutomationId = automationId, Exists = exists }
            ));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




}