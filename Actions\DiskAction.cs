﻿using System;
using System.Management;
using System.Diagnostics;
using System.Collections.Generic;



namespace DesktopRpaLib.Actions
{
    class DiskAction
    {

        // 快速格式化
        public void QuickFormat(string driveLetter, string fileSystem, string label)
        {

            // 确保驱动器号格式正确（例如："C:"）
            driveLetter = driveLetter.TrimEnd('\\', ':') + ":";

            // 创建 WMI 查询以获取卷对象
            string query = string.Format("SELECT * FROM Win32_Volume WHERE DriveLetter = '{0}'", driveLetter);

            using (ManagementObjectSearcher searcher = new ManagementObjectSearcher(query))
            {
                foreach (ManagementObject volume in searcher.Get())
                {
                    // 获取当前文件系统和卷标
                    string currentFileSystem = volume["FileSystem"]?.ToString() ?? "NTFS";
                    string currentLabel = volume["Label"]?.ToString() ?? "";

                    // 如果没有提供文件系统或卷标，则使用当前值
                    fileSystem = string.IsNullOrEmpty(fileSystem) ? currentFileSystem : fileSystem;
                    label = string.IsNullOrEmpty(label) ? currentLabel : label;

                    // 调试输出
                    Console.WriteLine($"Formatting {driveLetter} with FileSystem: {fileSystem} and Label: {label}");

                    // 调用 Format 方法进行快速格式化
                    ManagementBaseObject inParams = volume.GetMethodParameters("Format");
                    inParams["FileSystem"] = fileSystem;  // 使用当前文件系统
                    inParams["QuickFormat"] = true;              // 设置为快速格式化
                    inParams["Label"] = label;            // 使用当前卷标

                    ManagementBaseObject outParams = volume.InvokeMethod("Format", inParams, null);

                    // 检查返回值
                    uint returnValue = Convert.ToUInt32(outParams["ReturnValue"]);
                    if (returnValue != 0)
                    {
                        // 如果返回错误码为3，可能是权限问题
                        if (returnValue == 3)
                        {
                            throw new Exception("格式化失败, 无操作权限");
                        }

                        throw new Exception($"{driveLetter} 格式化失败, 错误码: {returnValue}");
                    }
                }
            }
        }




        // 获取指定驱动器的信息
        public Dictionary<string, object> GetVolumeInfo(string driveLetter)
        {
            try
            {
                // 确保驱动器号格式正确（例如："C:"）
                driveLetter = driveLetter.TrimEnd('\\', ':') + ":";

                // 创建 WMI 查询以获取卷对象
                string query = $"SELECT * FROM Win32_Volume WHERE DriveLetter = '{driveLetter}'";

                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher(query))
                {
                    foreach (ManagementObject volume in searcher.Get())
                    {
                        string fileSystem = volume["FileSystem"]?.ToString() ?? "";
                        string label = volume["Label"]?.ToString() ?? "";
                        ulong totalSize = Convert.ToUInt64(volume["Capacity"] ?? 0);
                        ulong freeSpace = Convert.ToUInt64(volume["FreeSpace"] ?? 0);
                        ulong usedSpace = totalSize - freeSpace;
                        uint driveType = Convert.ToUInt32(volume["DriveType"] ?? 0);
                        string volumeSerialNumber = volume["SerialNumber"]?.ToString() ?? "";
                        bool isBootable = Convert.ToBoolean(volume["BootVolume"] ?? false);
                        bool isSystem = Convert.ToBoolean(volume["SystemVolume"] ?? false);
                        bool isCompressed = Convert.ToBoolean(volume["Compressed"] ?? false);
                        bool supportsDiskQuotas = Convert.ToBoolean(volume["SupportsDiskQuotas"] ?? false);

                        // 根据 DriveType 设置 DriveTypeText
                        string driveTypeText = GetDriveTypeText(driveType);

                        // 计算TotalSizeRange和TotalSizeRangeText
                        int totalSizeRange;
                        string totalSizeRangeText;
                        if (fileSystem != "")
                        {
                            ( totalSizeRange,  totalSizeRangeText) = CalculateTotalSizeRange(totalSize);
                        }else
                        {
                            totalSizeRange = 0;
                            totalSizeRangeText = "";
                        }

                        // 将信息放入字典
                        var volumeInfo = new Dictionary<string, object>
                        {
                            { "DriveLetter", driveLetter.Replace(":", "")}, // 驱动器盘符
                            { "FileSystem", fileSystem },   // 文件系统
                            { "Label", label },// 卷标
                            { "TotalSize", totalSize }, // 总容量
                            { "TotalSizeFormat", FormatSize(totalSize) }, // 总容量
                            { "TotalSizeRange", totalSizeRange }, // 总容量范围
                            { "TotalSizeRangeText", totalSizeRangeText }, // 总容量范围文本
                            { "UsedSpace", usedSpace }, // 已用容量
                            { "UsedSpaceFormat", FormatSize(usedSpace) }, // 已用容量
                            { "FreeSpace", freeSpace },// 可用容量
                            { "FreeSpaceFormat", FormatSize(freeSpace) },// 可用容量
                            { "DriveType", driveType }, // 驱动器类型值
                            { "DriveTypeText", driveTypeText }, // 驱动器类型文本（例如：LocalDisk, Removable, NetworkDrive）
                            { "VolumeSerialNumber", volumeSerialNumber }, // 卷序列号
                            { "IsBootable", isBootable }, // 是否是启动磁盘
                            { "IsSystem", isSystem }, // 是否是系统盘
                            { "IsCompressed", isCompressed },// 是否启用了压缩
                            { "SupportsDiskQuotas", supportsDiskQuotas }, // 是否支持磁盘配额
                        };

                        return volumeInfo;
                    }
                }

                throw new Exception($"未找到驱动器 {driveLetter}");
            }
            catch (Exception ex)
            {
                throw new Exception($"获取驱动器 {driveLetter} 信息失败: {ex.Message}");
            }
        }


        // 辅助方法：根据 DriveType 获取文本描述
        private string GetDriveTypeText(uint driveType)
        {
            switch (driveType)
            {
                case 2:
                    return "Removable";
                case 3:
                    return "LocalDisk";
                case 4:
                    return "NetworkDrive";
                default:
                    return "Unknown";
            }
        }


        // 辅助方法：根据总容量计算范围分类及文本描述
        private (int, string) CalculateTotalSizeRange(ulong totalSize)
        {
            if (totalSize < (2UL * 1024 * 1024 * 1024))
            {
                return (2, "2GB");
            }
            else if (totalSize < (4UL * 1024 * 1024 * 1024))
            {
                return (4, "4GB");
            }
            else if (totalSize < (8UL * 1024 * 1024 * 1024))
            {
                return (8, "8GB");
            }
            else if (totalSize < (16UL * 1024 * 1024 * 1024))
            {
                return (16, "16GB");
            }
            else if (totalSize < (32UL * 1024 * 1024 * 1024))
            {
                return (32, "32GB");
            }
            else if (totalSize < (64UL * 1024 * 1024 * 1024))
            {
                return (64, "64GB");
            }
            else if (totalSize < (128UL * 1024 * 1024 * 1024))
            {
                return (128, "128GB");
            }
            else if (totalSize < (256UL * 1024 * 1024 * 1024))
            {
                return (256, "256GB");
            }
            else
            {
                return (512, "512GB及以上");
            }
        }


        // 辅助方法：格式化容量为易读的格式（比如 GB, MB）
        public string FormatSize(ulong sizeInBytes)
        {
            double sizeInGB = sizeInBytes / (1024.0 * 1024.0 * 1024.0);
            if (sizeInGB >= 1)
                return $"{sizeInGB:F2} GB";

            double sizeInMB = sizeInBytes / (1024.0 * 1024.0);
            if (sizeInMB >= 1)
                return $"{sizeInMB:F2} MB";

            double sizeInKB = sizeInBytes / 1024.0;
            return $"{sizeInKB:F2} KB";
        }




        // 获取所有可移动驱动器列表
        public List<string> GetRemovableDrives()
        {
            List<string> removableDrives = new List<string>();
            try
            {
                // 创建 WMI 查询以获取所有逻辑磁盘
                string query = "SELECT * FROM Win32_LogicalDisk WHERE DriveType = 2"; // 2 表示可移动磁盘

                using (ManagementObjectSearcher searcher = new ManagementObjectSearcher(query))
                {
                    foreach (ManagementObject disk in searcher.Get())
                    {
                        string driveLetter = disk["DeviceID"]?.ToString() ?? "";
                        if (!string.IsNullOrEmpty(driveLetter))
                        {
                            removableDrives.Add(driveLetter.Replace(":", ""));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取可移动磁盘驱动器列表失败, {ex.Message}");
            }

            return removableDrives;
        }






    }
}
