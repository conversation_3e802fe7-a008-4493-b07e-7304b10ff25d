﻿using System.Web.Http;
using DesktopRpaLib.Actions;
using DesktopRpaLib.Response;
using System;



[RoutePrefix("api/radioButton/{ProcessId}/{AutomationId}")] // 定义路由前缀
public class RadioButtonController : ApiController
{
    private readonly RadioButtonAction _radioButtonAction = new RadioButtonAction();



    // 获取单选按钮的文本内容
    [Route("getText")]
    public IHttpActionResult GetRadioButtonText(int ProcessId, string AutomationId)
    {
        try
        {
            var radioButtonText = _radioButtonAction.GetRadioButtonText(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, RadioButtonText = radioButtonText };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 获取单选按钮是否可用
    [Route("isEnabled")]
    public IHttpActionResult GetIsRadioButtonEnabled(int ProcessId, string AutomationId)
    {
        try
        {
            var isEnabled = _radioButtonAction.IsRadioButtonEnabled(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, IsEnabled = isEnabled };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 点击单选按钮
    [Route("click")]
    public IHttpActionResult PostClickRadioButton(int ProcessId, string AutomationId)
    {
        try
        {
            _radioButtonAction.ClickRadioButton(ProcessId, AutomationId);
            return Json(ResponseResult<object>.Success(msg: "单选按钮状态已切换"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }
}
