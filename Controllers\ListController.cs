﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using DesktopRpaLib.Actions;
using DesktopRpaLib.Response;



[RoutePrefix("api/list/{ProcessId}/{AutomationId}")] // 定义路由前缀
public class ListController : ApiController
{

    private readonly ListAction _listAction = new ListAction();




    // 选择指定的 ListItem
    [Route("select")]
    public IHttpActionResult PostSelectListItemByName(int ProcessId, string AutomationId, [FromUri] string itemName)
    {
        if (string.IsNullOrEmpty(itemName))
        {
            return Json(ResponseResult<string>.Error(msg: "itemName参数不能为空"));
        }

        try
        {
            _listAction.SelectListViewItemByName(ProcessId, AutomationId, itemName);
            return Json(ResponseResult<object>.Success(msg: "ListViewItem已选择"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }



    // 获取所有ListViewItem的名称
    [Route("getItemNames")]
    public IHttpActionResult GetListViewItemNames(int ProcessId, string AutomationId)
    {
        try
        {
            var itemNames = _listAction.GetListViewItemNames(ProcessId, AutomationId);
            return Json(ResponseResult<List<string>>.Success(data: itemNames, msg: "成功获取所有ListViewItem名称"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }


}

