﻿using System;
using System.Diagnostics;
using System.Windows.Automation;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;


namespace DesktopRpaLib.Actions
{
    class ElementAction
    {



        // 根据元素的 Name 判断其是否存在
        public bool isExistByName(int processId, string elementName)
        {
            var mainWindow = ActionUtil.GetMainWindow(processId);

            try
            {
                // 使用 AutomationElement.NameProperty 根据元素的名称查找该元素
                var condition = new PropertyCondition(AutomationElement.NameProperty, elementName);
                var element = mainWindow.FindFirst(TreeScope.Descendants, condition);
                return element != null;
            }
            catch (Exception)
            {
                return false;
            }
        }



        // 根据 AutomationId 判断元素是否存在
        public bool isExistByAutomationId(int processId, string automationId)
        {
            var mainWindow = ActionUtil.GetMainWindow(processId);

            try
            {
                // 使用 AutomationElement.AutomationIdProperty 根据元素的 AutomationId 查找该元素
                var condition = new PropertyCondition(AutomationElement.AutomationIdProperty, automationId);
                var element = mainWindow.FindFirst(TreeScope.Descendants, condition);
                return element != null;
            }
            catch (Exception)
            {
                return false;
            }
        }






    }
}
