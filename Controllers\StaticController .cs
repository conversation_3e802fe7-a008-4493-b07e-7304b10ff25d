﻿using System.Web.Http;
using DesktopRpaLib.Actions;
using DesktopRpaLib.Response;
using System;

[RoutePrefix("api/static/{ProcessId}/{AutomationId}")] // 定义路由前缀
public class StaticController : ApiController
{
    private readonly StaticAction _staticAction = new StaticAction();

    // 获取静态文本内容
    [Route("getText")]
    public IHttpActionResult GetStaticText(int ProcessId, string AutomationId)
    {
        try
        {
            var staticText = _staticAction.GetStaticText(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, StaticText = staticText };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }
}
