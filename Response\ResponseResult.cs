﻿
namespace DesktopRpaLib.Response
{

    public class ResponseResult<T>
    {
        public string Name { get; set; } // 名称
        public string Msg { get; set; } // 响应消息
        public T Data { get; set; } // 数据

        public ResponseResult() { }

        public ResponseResult(string name, string msg, T data)
        {
            Name = name;
            Msg = msg;
            Data = data;
        }

        /*
            构建成功响应的方法
         */
        public static ResponseResult<T> Success(string name = "SUCCESS", string msg = "成功", T data = default)
        {
            return new ResponseResult<T>(name, msg, data);
        }

        /*
            构建失败响应的方法
         */
        public static ResponseResult<T> Error(string name = "ERROR", string msg = "失败", T data = default)
        {
            return new ResponseResult<T>(name, msg, data);
        }



    }
}
