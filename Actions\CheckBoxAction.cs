﻿using System;
using System.Diagnostics;
using System.Windows.Automation;

namespace DesktopRpaLib.Actions
{
    class CheckBoxAction
    {


        // 复选框是否可用
        public bool IsCheckBoxEnabled(int processId, string automationId)
        {
            return ActionUtil.IsElementEnabled(processId, automationId);
        }



        // 获取复选框的文本内容
        public string GetCheckBoxText(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var checkBoxText = element.GetCurrentPropertyValue(AutomationElement.NameProperty)?.ToString();
            if (string.IsNullOrEmpty(checkBoxText))
            {
                throw new Exception("复选框文本为空或未设置");
            }

            return checkBoxText;
        }



        // 切换复选框状态
        public void ClickCheckBox(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            // 获取 TogglePattern 来操作复选框的选中状态
            var togglePattern = element.GetCurrentPattern(TogglePattern.Pattern) as TogglePattern;
            if (togglePattern != null)
            {
                togglePattern.Toggle();
            }
            else
            {
                throw new Exception("无法获取复选框的 TogglePattern");
            }
        }



        // 检查复选框是否被勾选
        public bool IsCheckBoxChecked(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            // 获取 TogglePattern 来检查复选框的选中状态
            var togglePattern = element.GetCurrentPattern(TogglePattern.Pattern) as TogglePattern;
            if (togglePattern != null)
            {
                return togglePattern.Current.ToggleState == ToggleState.On; // 返回勾选状态
            }
            else
            {
                throw new Exception("无法获取复选框的 TogglePattern");
            }
        }



        // 勾选复选框
        public void Check(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);
            var togglePattern = element.GetCurrentPattern(TogglePattern.Pattern) as TogglePattern;

            if (togglePattern != null)
            {
                if (togglePattern.Current.ToggleState == ToggleState.Off)
                {
                    togglePattern.Toggle(); // 勾选复选框
                }
            }
            else
            {
                throw new Exception("无法获取复选框的 TogglePattern");
            }
        }




        // 取消勾选复选框
        public void Uncheck(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);
            var togglePattern = element.GetCurrentPattern(TogglePattern.Pattern) as TogglePattern;

            if (togglePattern != null)
            {
                if (togglePattern.Current.ToggleState == ToggleState.On)
                {
                    togglePattern.Toggle(); // 取消勾选复选框
                }
            }
            else
            {
                throw new Exception("无法获取复选框的 TogglePattern");
            }
        }






    }
}
