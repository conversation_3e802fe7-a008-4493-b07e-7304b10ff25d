#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面RPA API测试脚本
使用方法: python test_api.py
"""

import requests
import json
import time
import sys

API_BASE = "http://localhost:9000"

def print_result(title, response):
    """打印测试结果"""
    print(f"\n{'='*50}")
    print(f"测试: {title}")
    print(f"{'='*50}")
    
    try:
        if response.status_code == 200:
            data = response.json()
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
            return data.get('success', False)
        else:
            print(f"HTTP错误: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"解析响应失败: {e}")
        return False

def test_basic_apis():
    """测试基础API"""
    print("开始测试基础API...")
    
    # 1. 健康检查
    try:
        response = requests.get(f"{API_BASE}/api/hello", timeout=5)
        print_result("健康检查", response)
    except requests.exceptions.RequestException as e:
        print(f"健康检查失败: {e}")
        print("请确保API服务已启动在 http://localhost:9000")
        return False
    
    # 2. 获取串口列表
    try:
        response = requests.get(f"{API_BASE}/api/serialPort/getPortNames", timeout=5)
        print_result("获取串口列表", response)
    except requests.exceptions.RequestException as e:
        print(f"获取串口列表失败: {e}")
    
    # 3. 获取可移动驱动器
    try:
        response = requests.get(f"{API_BASE}/api/disk/getRemovableDrives", timeout=5)
        print_result("获取可移动驱动器", response)
    except requests.exceptions.RequestException as e:
        print(f"获取可移动驱动器失败: {e}")
    
    return True

def test_keyboard_api():
    """测试键盘API"""
    print("\n开始测试键盘API...")
    
    keys = ["enter", "tab", "up", "down"]
    for key in keys:
        try:
            response = requests.post(f"{API_BASE}/api/keyboard/pressKey/{key}", timeout=5)
            print_result(f"模拟按键 {key}", response)
            time.sleep(0.5)  # 避免按键过快
        except requests.exceptions.RequestException as e:
            print(f"模拟按键 {key} 失败: {e}")

def test_process_api():
    """测试进程API"""
    print("\n开始测试进程API...")
    
    # 测试获取记事本进程
    process_names = ["notepad", "explorer", "winlogon"]
    
    for process_name in process_names:
        try:
            response = requests.get(f"{API_BASE}/api/exe/getPidsByExeName", 
                                  params={"exeName": process_name}, timeout=5)
            result = print_result(f"获取 {process_name} 进程PID", response)
            
            # 如果成功获取到PID，尝试获取进程信息
            if result and response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data', {}).get('AllPids'):
                    pid = data['data']['AllPids'][0]
                    try:
                        info_response = requests.get(f"{API_BASE}/api/exe/getExeNameAndTitle/{pid}", timeout=5)
                        print_result(f"获取进程 {pid} 信息", info_response)
                    except requests.exceptions.RequestException as e:
                        print(f"获取进程 {pid} 信息失败: {e}")
                    break
        except requests.exceptions.RequestException as e:
            print(f"获取 {process_name} 进程失败: {e}")

def test_window_api():
    """测试窗口API"""
    print("\n开始测试窗口API...")
    
    # 测试检查窗口是否存在
    window_titles = ["Program Manager", "桌面", "Desktop"]
    
    for title in window_titles:
        try:
            response = requests.get(f"{API_BASE}/api/window/windowExistsByTitle", 
                                  params={"title": title}, timeout=5)
            print_result(f"检查窗口 '{title}' 是否存在", response)
        except requests.exceptions.RequestException as e:
            print(f"检查窗口 '{title}' 失败: {e}")

def test_element_api():
    """测试元素API（需要实际的进程ID）"""
    print("\n开始测试元素API...")
    
    # 首先尝试获取一个进程ID
    try:
        response = requests.get(f"{API_BASE}/api/exe/getPidsByExeName", 
                              params={"exeName": "explorer"}, timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('data', {}).get('AllPids'):
                pid = data['data']['AllPids'][0]
                
                # 测试检查元素是否存在
                element_names = ["确定", "取消", "OK", "Cancel"]
                for element_name in element_names:
                    try:
                        response = requests.get(f"{API_BASE}/api/element/{pid}/isExistByName", 
                                              params={"elementName": element_name}, timeout=5)
                        print_result(f"检查元素 '{element_name}' 是否存在", response)
                    except requests.exceptions.RequestException as e:
                        print(f"检查元素 '{element_name}' 失败: {e}")
    except requests.exceptions.RequestException as e:
        print(f"获取进程ID失败: {e}")

def run_comprehensive_test():
    """运行综合测试"""
    print("桌面RPA API综合测试开始...")
    print(f"API服务地址: {API_BASE}")
    
    # 测试API服务是否可用
    if not test_basic_apis():
        print("\n基础API测试失败，请检查服务是否启动")
        return
    
    # 运行各项测试
    test_keyboard_api()
    test_process_api()
    test_window_api()
    test_element_api()
    
    print(f"\n{'='*50}")
    print("测试完成！")
    print(f"{'='*50}")

def interactive_test():
    """交互式测试"""
    while True:
        print(f"\n{'='*50}")
        print("桌面RPA API交互式测试")
        print(f"{'='*50}")
        print("1. 基础API测试")
        print("2. 键盘操作测试")
        print("3. 进程操作测试")
        print("4. 窗口操作测试")
        print("5. 元素操作测试")
        print("6. 运行全部测试")
        print("0. 退出")
        
        choice = input("\n请选择测试项目 (0-6): ").strip()
        
        if choice == "0":
            print("退出测试")
            break
        elif choice == "1":
            test_basic_apis()
        elif choice == "2":
            test_keyboard_api()
        elif choice == "3":
            test_process_api()
        elif choice == "4":
            test_window_api()
        elif choice == "5":
            test_element_api()
        elif choice == "6":
            run_comprehensive_test()
        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_test()
    else:
        run_comprehensive_test()
