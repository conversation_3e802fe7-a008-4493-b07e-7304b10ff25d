﻿using System.Web.Http;
using DesktopRpaLib.Response;
using DesktopRpaLib.Actions;
using System;
using System.Runtime.InteropServices;
using System.Text;
using System.Diagnostics;
using System.Threading;
using FlaUI.Core;
using FlaUI.UIA3;
using FlaUI.Core.AutomationElements;
using System.Collections.Generic;
using System.IO;
using System.Text.RegularExpressions;




[RoutePrefix("api/window")] // 定义路由前缀
public class WindowController : ApiController
{


    [DllImport("user32.dll")]
    static extern bool EnumWindows(EnumWindowsProc lpEnumFunc, IntPtr lParam);

    [DllImport("user32.dll")]
    static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

    [DllImport("user32.dll")]
    static extern bool IsWindowVisible(IntPtr hWnd);

    [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
    static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

    private const uint BM_CLICK = 0x00F5;

    delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

    [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
    static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

    [DllImport("user32.dll", SetLastError = true)]
    static extern bool SetForegroundWindow(IntPtr hWnd);

    [DllImport("user32.dll", SetLastError = true)]
    static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, uint dwExtraInfo);

    private const byte VK_MENU = 0x12; // Alt key
    private const byte VK_Y = 0x59;   // Y key
    private const uint KEYEVENTF_KEYUP = 0x0002; // Key up flag





    private readonly WindowAction _windowAction = new WindowAction();





    // 获取状态栏的文本
    [Route("getStatusBarText/{processId:int}")]
    public IHttpActionResult GetStatusBarText(int processId)
    {
        try
        {
            // 调用 _windowAction 来设置窗口大小
            var text = _windowAction.GetStatusBarText(processId);
            var data = new { Text = text };

            return Json(ResponseResult<object>.Success(msg: "获取状态栏文本成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }





    // 设置窗口大小
    [Route("setSize/{processId:int}/{width:int}/{height:int}")]
    public IHttpActionResult PostSetWindowSize(int processId, int width, int height)
    {
        try
        {
            // 调用 _windowAction 来设置窗口大小
            _windowAction.SetWindowSize(processId, width, height);

            return Json(ResponseResult<object>.Success(msg: "窗口大小设置成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 窗口恢复（如果最小化）
    [Route("restore/{processId:int}")]
    public IHttpActionResult PostRestoreWindow(int processId)
    {
        try
        {
            _windowAction.RestoreWindow(processId);

            return Json(ResponseResult<object>.Success(msg: "窗口恢复成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }


    // 最小化
    [Route("minimize/{processId:int}")]
    public IHttpActionResult PostMinimizeWindow(int processId)
    {
        try
        {
            _windowAction.MinimizeWindow(processId);

            return Json(ResponseResult<object>.Success(msg: "窗口最小化成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }



    // 最大化
    [Route("maximize/{processId:int}")]
    public IHttpActionResult PostMaximizeWindow(int processId)
    {
        try
        {
            _windowAction.MaximizeWindow(processId);

            return Json(ResponseResult<object>.Success(msg: "窗口最大化成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 窗口置顶
    [Route("top/{processId:int}")]
    public IHttpActionResult PostTopWindow(int processId)
    {
        try
        {
            _windowAction.TopWindow(processId);

            return Json(ResponseResult<object>.Success(msg: "窗口置顶成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 窗口获取焦点（临时置顶）
    [Route("focus/{processId:int}")]
    public IHttpActionResult PostFocusWindow(int processId)
    {
        try
        {
            _windowAction.FocusWindow(processId);

            return Json(ResponseResult<object>.Success(msg: "窗口获取焦点成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 移动窗口到指定xy坐标
    [Route("move/{processId:int}/{x:int}/{y:int}")]
    public IHttpActionResult PostMoveWindow(int processId, int x, int y)
    {
        try
        {
            _windowAction.MoveWindow(processId, x, y);

            return Json(ResponseResult<object>.Success(msg: "窗口移动成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }



    // 获取窗口的宽度和高度
    [Route("sizePos/{processId:int}")]
    public IHttpActionResult GetWindowSizePos(int processId)
    {
        try
        {
            var (width, height, x, y) = _windowAction.GetWindowSizePos(processId);
            return Json(ResponseResult<object>.Success(data: new { Width = width, Height = height, X = x, Y = y }, msg: "获取窗口大小和坐标成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }


    // 获取对话框的宽度和高度
    [Route("dialogSizePos/{processId:int}")]
    public IHttpActionResult GetDialogSizePos(int processId, [FromUri] string dialogName)
    {
        try
        {
            if (string.IsNullOrEmpty(dialogName))
            {
                return Json(ResponseResult<string>.Error(msg: "dialogName参数不能为空"));
            }

            var (width, height, x, y) = _windowAction.GetDialogSizePos(processId, dialogName);
            return Json(ResponseResult<object>.Success(data: new { Width = width, Height = height, X = x, Y = y }, msg: "获取对话框大小和坐标成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }





    // 点击指定坐标
    [Route("click/{processId:int}/{x:int}/{y:int}")]
    public IHttpActionResult PostClickAt(int processId, int x, int y)
    {
        try
        {
            _windowAction.ClickWindow(processId, x, y);
            return Json(ResponseResult<object>.Success(msg: "点击成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }



    // 关闭对话框
    [Route("closeDialog/{processId:int}")]
    public IHttpActionResult PostCloseDialog(int processId, [FromUri] string dialogName)
    {

        if (string.IsNullOrEmpty(dialogName))
        {
            return Json(ResponseResult<string>.Error(msg: "dialogName参数不能为空"));
        }


        try
        {
            _windowAction.CloseDialog(processId, dialogName);
            return Json(ResponseResult<object>.Success(msg: "关闭成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }










    // 移动对话框到指定xy坐标
    [Route("moveDialog/{processId:int}/{x:int}/{y:int}")]
    public IHttpActionResult PostMoveDialog(int processId, int x, int y, [FromUri] string dialogName)
    {
        try
        {
            _windowAction.MoveDialog(processId, x, y, dialogName);
            return Json(ResponseResult<object>.Success(msg: "对话框移动成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }






    // 检查对话框是否存在
    [Route("dialogExists/{processId:int}")]
    public IHttpActionResult GetDialogExists(int processId, [FromUri] string dialogName)
    {
        if (string.IsNullOrEmpty(dialogName))
        {
            return Json(ResponseResult<string>.Error(msg: "dialogName参数不能为空"));
        }

        try
        {
            bool exists = _windowAction.DialogExists(processId, dialogName);
            return Json(ResponseResult<object>.Success(data: new { Exists = exists }, msg: "对话框存在"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }





    // 检查对话框是否存在
    [Route("windowExistsByTitle")]
    public IHttpActionResult GetWindowExistsByTitle([FromUri] string title)
    {
        if (string.IsNullOrEmpty(title))
        {
            return Json(ResponseResult<string>.Error(msg: "title参数不能为空"));
        }

        try
        {
            bool exists = _windowAction.WindowExistsByTitle(title);
            return Json(ResponseResult<object>.Success(data: new { Exists = exists }, msg: "窗口存在"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }


    // 截取窗口并保存到指定路径
    [Route("captureByTailor/{processId:int}")]
    public IHttpActionResult PostCaptureWindowByTailor(int processId, [FromUri] string savePath)
    {
        if (string.IsNullOrEmpty(savePath))
        {
            return Json(ResponseResult<string>.Error(msg: "savePath参数不能为空"));
        }

        // 仅支持png格式后缀
        if (!savePath.EndsWith(".png", StringComparison.OrdinalIgnoreCase))
        {
            return Json(ResponseResult<string>.Error(msg: "仅支持png格式后缀"));
        }

        // 检查路径是否合法
        if (!IsValidWindowsPath(savePath))
        {
            return Json(ResponseResult<string>.Error(msg: "savePath格式不合法"));
        }

        // 目标目录不存在
        if (!Directory.Exists(Path.GetDirectoryName(savePath)))
        {
            return Json(ResponseResult<string>.Error(msg: "目标目录不存在"));
        }

        try
        {
            _windowAction.CaptureWindowByTailor(processId, savePath);
            return Json(ResponseResult<object>.Success(msg: "截图成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 截取窗口并保存到指定路径
    [Route("captureByHandle/{processId:int}")]
    public IHttpActionResult PostCaptureWindowByHandle(int processId, [FromUri] string savePath)
    {
        if (string.IsNullOrEmpty(savePath))
        {
            return Json(ResponseResult<string>.Error(msg: "savePath参数不能为空"));
        }

        // 仅支持png格式后缀
        if (!savePath.EndsWith(".png", StringComparison.OrdinalIgnoreCase))
        {
            return Json(ResponseResult<string>.Error(msg: "仅支持png格式后缀"));
        }

        // 检查路径是否合法
        if (!IsValidWindowsPath(savePath))
        {
            return Json(ResponseResult<string>.Error(msg: "savePath格式不合法"));
        }

        // 目标目录不存在
        if (!Directory.Exists(Path.GetDirectoryName(savePath)))
        {
            return Json(ResponseResult<string>.Error(msg: "目标目录不存在"));
        }

        try
        {
            _windowAction.CaptureWindowByHandle(processId, savePath);
            return Json(ResponseResult<object>.Success(msg: "截图成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }






    // 检查目录是否合法
    public bool IsValidWindowsPath(string path)
    {
        return true;
    }







}