﻿using System;
using System.Diagnostics;
using System.Windows.Automation;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;


namespace DesktopRpaLib.Actions
{
    class KeyBoardAction
    {

        // 通过虚拟键码模拟按键
        [DllImport("user32.dll", SetLastError = true)]
        public static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, uint dwExtraInfo);


        // 定义虚拟键码
        private static readonly Dictionary<string, byte> KeyMap = new Dictionary<string, byte>
        {
            { "up", 0x26 },    // 上箭头键
            { "down", 0x28 },  // 下箭头键
            { "left", 0x25 },  // 左箭头键
            { "right", 0x27 }, // 右箭头键
            { "tab", 0x09 },   // Tab 键
            { "enter", 0x0D }  // Enter 键
        };


        // 模拟单个按键
        public void PressKey(string key)
        {
            try
            {
                // 查找虚拟键码
                if (KeyMap.ContainsKey(key.ToLower()))
                {
                    byte keyCode = KeyMap[key.ToLower()];

                    // 按下按键
                    keybd_event(keyCode, 0, 0, 0);

                    // 释放按键
                    keybd_event(keyCode, 0, 2, 0);
                }
                else
                {
                    throw new Exception($"无法识别的按键: {key}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"模拟按键 {key} 失败: {ex.Message}");
            }
        }





    }
}
