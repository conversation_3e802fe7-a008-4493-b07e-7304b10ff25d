﻿using System;
using System.Collections.Generic;
using System.Web.Http;
using DesktopRpaLib.Actions;
using DesktopRpaLib.Response;



[RoutePrefix("api/disk")]  // 定义路由前缀
public class DiskController : ApiController
{
    private readonly DiskAction _diskAction = new DiskAction();



    // 获取所有可移动驱动器列表
    [Route("getRemovableDrives")]
    public IHttpActionResult GetRemovableDrives()
    {
        try
        {
            var removableDrives = _diskAction.GetRemovableDrives();
            var data = new { RemovableDrives = removableDrives };
            return Json(ResponseResult<object>.Success(msg: "查询可移动驱动器成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }





    // 获取指定驱动器的信息
    [Route("getVolumeInfo")]
    public IHttpActionResult GetVolumeInfo([FromUri] string driveLetter)
    {

        if (string.IsNullOrEmpty(driveLetter))
        {
            return Json(ResponseResult<string>.Error(msg: "driveLetter 参数不能为空"));
        }


        try
        {
            var volumeInfo = _diskAction.GetVolumeInfo(driveLetter);
            var data = new { VolumeInfo = volumeInfo };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 快速格式化
    [Route("quickFormat")]
    public IHttpActionResult PostQuickFormat([FromUri] string driveLetter, [FromUri] string fileSystem = null, [FromUri] string label = null)
    {
        if (string.IsNullOrEmpty(driveLetter))
        {
            return Json(ResponseResult<string>.Error(msg: "driveLetter 参数不能为空"));
        }

        try
        {
            _diskAction.QuickFormat(driveLetter, fileSystem, label);
            var data = new { DriveLetter = driveLetter, FileSystem = fileSystem, Label = label };
            return Json(ResponseResult<object>.Success(msg: $"{driveLetter} 快速格式化成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }








}
