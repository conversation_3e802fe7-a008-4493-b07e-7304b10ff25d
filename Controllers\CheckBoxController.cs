﻿using System.Web.Http;
using DesktopRpaLib.Actions;
using DesktopRpaLib.Response;
using System;

[RoutePrefix("api/checkbox/{ProcessId}/{AutomationId}")] // 定义路由前缀
public class CheckBoxController : ApiController
{
    private readonly CheckBoxAction _checkBoxAction = new CheckBoxAction();

    // 获取复选框文本内容
    [Route("getText")]
    public IHttpActionResult GetCheckBoxText(int ProcessId, string AutomationId)
    {
        try
        {
            var checkBoxText = _checkBoxAction.GetCheckBoxText(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, CheckBoxText = checkBoxText };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 获取复选框是否可用
    [Route("isEnabled")]
    public IHttpActionResult GetIsCheckBoxEnabled(int ProcessId, string AutomationId)
    {
        try
        {
            var isEnabled = _checkBoxAction.IsCheckBoxEnabled(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, IsEnabled = isEnabled };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 点击复选框（选择/取消选择）
    [Route("click")]
    public IHttpActionResult PostClickCheckBox(int ProcessId, string AutomationId)
    {
        try
        {
            _checkBoxAction.ClickCheckBox(ProcessId, AutomationId);
            return Json(ResponseResult<object>.Success(msg: "复选框状态已切换"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }


    // 获取复选框的勾选状态
    [Route("isChecked")]
    public IHttpActionResult GetIsCheckBoxChecked(int ProcessId, string AutomationId)
    {
        try
        {
            var isChecked = _checkBoxAction.IsCheckBoxChecked(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, IsChecked = isChecked };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }



    // 勾选复选框
    [Route("check")]
    public IHttpActionResult PostCheckCheckBox(int ProcessId, string AutomationId)
    {
        try
        {
            _checkBoxAction.Check(ProcessId, AutomationId);
            return Json(ResponseResult<object>.Success(msg: "复选框已勾选"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 取消勾选复选框
    [Route("uncheck")]
    public IHttpActionResult PostUncheckCheckBox(int ProcessId, string AutomationId)
    {
        try
        {
            _checkBoxAction.Uncheck(ProcessId, AutomationId);
            return Json(ResponseResult<object>.Success(msg: "复选框已取消勾选"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }





}
