﻿// HelloWorldController.cs
using System.Web.Http;
using DesktopRpaLib.Response;
using DesktopRpaLib.Actions;
using System;
using System.Threading.Tasks;




// 按钮接口
[RoutePrefix("api/button/{ProcessId}/{AutomationId}")] // 定义路由前缀
public class ButtonController : ApiController // 继承自ApiController，定义一个Web API控制器
{

    private readonly ButtonAction _buttonAction = new ButtonAction();



    // 检查按钮是否可用
    [Route("isEnabled")]
    public IHttpActionResult GetIsEnabled(int ProcessId, string AutomationId)
    {
        try
        {
            var isEnabled = _buttonAction.IsButtonEnabled(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, IsEnabled = isEnabled };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }



    // 获取按钮文本
    [Route("getText")]
    public IHttpActionResult GetButtonText(int ProcessId, string AutomationId)
    {
        try
        {
            var buttonText = _buttonAction.GetButtonText(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, ButtonText = buttonText };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }



    // 单击按钮
    [Route("click")]
    public IHttpActionResult PostClickButton(int ProcessId, string AutomationId)
    {
        try
        {
            _buttonAction.ClickButton(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId };
            return Json(ResponseResult<object>.Success(msg: "单击成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }


    // 异步单击按钮
    [Route("clickAsync")]
    public IHttpActionResult PostClickButtonAsync(int ProcessId, string AutomationId)
    {

        try
        {
            _buttonAction.ClickButtonAsync(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId };
            return Json(ResponseResult<object>.Success(msg: "异步单击成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }






    // 双击按钮
    [Route("doubleClick")]
    public IHttpActionResult PostDoubleClickButton(int ProcessId, string AutomationId)
    {
        try
        {
            _buttonAction.DoubleClickButton(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId };
            return Json(ResponseResult<object>.Success(msg: "双击成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }
}


