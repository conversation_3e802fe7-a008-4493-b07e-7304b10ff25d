﻿using System;
using System.Diagnostics;
using System.Windows.Automation;
using System.Collections.Generic;




namespace DesktopRpaLib.Actions
{
    public class TabAction
    {




        // 选择选项卡项目
        public void SelectTabItemByName(int processId, string automationId, string targetName)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            // 查找并选择特定的 Tab 项目
            var tabItem = ActionUtil.FindElementByName(element, targetName);
            if (tabItem != null)
            {
                // 使用 SelectionItemPattern 或 InvokePattern 来选择 Tab 项目
                if (tabItem.TryGetCurrentPattern(SelectionItemPattern.Pattern, out var patternObj))
                {
                    var selectionItemPattern = patternObj as SelectionItemPattern;
                    selectionItemPattern.Select();
                }
                else if (tabItem.TryGetCurrentPattern(InvokePattern.Pattern, out patternObj))
                {
                    var invokePattern = patternObj as InvokePattern;
                    invokePattern.Invoke();
                }
                else
                {
                    throw new Exception("无法选择 Tab 项目");
                }
            }
            else
            {
                throw new Exception("未找到匹配的 Tab 项目");
            }

        }




        // 获取所有匹配的 TabItem 控件
        //public List<string> GetAllTabNames(int processId, string automationId)
        //{
        //    var mainWindow = ActionUtil.GetMainWindow(processId);
        //    if (mainWindow == null)
        //    {
        //        throw new Exception("无法获取主窗口");
        //    }

        //    // 查找所有 Tab 项目
        //    var tabItems = ActionUtil.FindElementsByAutomationId(mainWindow, automationId);
        //    var tabNames = new List<string>();

        //    if (tabItems.Count == 0)
        //    {
        //        Console.WriteLine("未找到匹配的 TabItem 控件。");
        //    }

        //    foreach (var tabItem in tabItems)
        //    {
        //        var tabName = tabItem.GetCurrentPropertyValue(AutomationElement.NameProperty)?.ToString();
        //        if (!string.IsNullOrEmpty(tabName))
        //        {
        //            tabNames.Add(tabName);
        //        }
        //    }

        //    return tabNames;
        //}





    }
}
