<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Owin.Cors</name>
    </assembly>
    <members>
        <member name="T:Owin.CorsExtensions">
            <summary>
            Extension methods for adding CorsMiddleware to an application pipeline.
            </summary>
        </member>
        <member name="M:Owin.CorsExtensions.UseCors(Owin.IAppBuilder,Microsoft.Owin.Cors.CorsOptions)">
            <summary>
            Adds a CORS middleware to your web application pipeline to allow cross domain requests.
            </summary>
            <param name="app">The IAppBuilder passed to your configuration method</param>
            <param name="options">An options class that controls the middleware behavior</param>
            <returns>The original app parameter</returns>
        </member>
        <member name="T:Microsoft.Owin.Cors.CorsMiddleware">
            <summary>
            Processes requests according to the provided cross domain policy.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Cors.CorsMiddleware.#ctor(System.Func{System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.Tasks.Task},Microsoft.Owin.Cors.CorsOptions)">
            <summary>
            Creates a new instance of CorsMiddleware.
            </summary>
            <param name="next"></param>
            <param name="options"></param>
        </member>
        <member name="M:Microsoft.Owin.Cors.CorsMiddleware.Invoke(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Evaluates and applies the CORS policy. Responses will be generated for preflight requests.
            Requests that are permitted by the CORS policy will be passed onto the next middleware.
            </summary>
            <param name="environment"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Owin.Cors.CorsOptions">
            <summary>
            Contains the options used by the CorsMiddleware
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Cors.CorsOptions.AllowAll">
            <summary>
            A policy that allows all headers, all methods, any origin and supports credentials
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Cors.CorsOptions.PolicyProvider">
            <summary>
            The cors policy to apply
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Cors.CorsOptions.CorsEngine">
            <summary>
            The cors engine
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Cors.CorsPolicyProvider">
            <summary>
            A pluggable CORS policy provider that always returns null by default.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Cors.CorsPolicyProvider.#ctor">
            <summary>
            Creates a new CorsPolicyProvider instance.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Cors.CorsPolicyProvider.PolicyResolver">
            <summary>
            A pluggable callback that will be used to select the CORS policy for the given requests.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Cors.CorsPolicyProvider.GetCorsPolicyAsync(Microsoft.Owin.IOwinRequest)">
            <summary>
            Executes the PolicyResolver unless overridden by a subclass.
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="T:Microsoft.Owin.Cors.ICorsPolicyProvider">
            <summary>
            Defines how to select a CORS policy for a given request.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Cors.ICorsPolicyProvider.GetCorsPolicyAsync(Microsoft.Owin.IOwinRequest)">
            <summary>
            Selects a CORS policy to apply for the given request.
            </summary>
            <param name="request"></param>
            <returns>The CORS policy to apply to the request, or null if no policy applies and
            the request should be passed through to the next middleware.</returns>
        </member>
    </members>
</doc>
