﻿using FlaUI.Core;
using FlaUI.Core.AutomationElements;
using FlaUI.Core.Conditions;
using FlaUI.UIA3;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;



namespace DesktopRpaLib.Actions
{
    public class MenuAction
    {



        // 获取顶层菜单
        public List<string> GetTopMenus(int processId)
        {
            List<string> topMenus = new List<string>();

            Process process = Process.GetProcessById(processId)
              ?? throw new Exception("processId未找到");

            using (var automation = new UIA3Automation())
            {
                var app = Application.Attach(process)
                 ?? throw new Exception("无法找到应用");

                var mainWindow = app.GetMainWindow(automation)
                ?? throw new Exception("无法找到主窗口");

                // 添加 Name 条件，查找应用程序的菜单栏
                var menuBar = mainWindow.FindFirstDescendant(cf =>
                    cf.ByControlType(FlaUI.Core.Definitions.ControlType.MenuBar)
                    .And(cf.ByName("应用程序")));

                if (menuBar == null) return topMenus;

                var menus = menuBar.FindAllChildren();
                foreach (var menu in menus)
                {
                    if (menu != null && !string.IsNullOrEmpty(menu.Name))
                    {
                        topMenus.Add(menu.Name);
                    }
                }

            }

            return topMenus;


        }





        // 选择菜单
        public void SelectMenu(int processId, string menuName)
        {
            Process process = Process.GetProcessById(processId)
                ?? throw new Exception("processId未找到");

            using (var automation = new UIA3Automation())
            {
                var app = Application.Attach(process)
                    ?? throw new Exception("无法找到应用");

                var mainWindow = app.GetMainWindow(automation)
                    ?? throw new Exception("无法找到主窗口");

                // 查找应用程序的菜单栏
                var menuBar = mainWindow.FindFirstDescendant(cf =>
                    cf.ByControlType(FlaUI.Core.Definitions.ControlType.MenuBar)
                    .And(cf.ByName("应用程序")));

                if (menuBar == null) return;

                // 查找子菜单
                var subMenu = menuBar.FindFirstChild(cf =>
                    cf.ByControlType(FlaUI.Core.Definitions.ControlType.MenuItem)
                    .And(cf.ByName(menuName)));

                if (subMenu == null) return;

                // 点击子菜单
                subMenu.Focus();
                subMenu.Click();

            }
        }



        // 根据 automationId 选择子菜单
        public void SelectMenuByAutomationId(int processId, string automationId)
        {
            Process process = Process.GetProcessById(processId)
                ?? throw new Exception("processId未找到");

            using (var automation = new UIA3Automation())
            {
                var app = Application.Attach(process)
                    ?? throw new Exception("无法找到应用");

                var mainWindow = app.GetMainWindow(automation)
                    ?? throw new Exception("无法找到主窗口");

                // 直接通过 automationId 查找菜单项
                var menuItem = mainWindow.FindFirstDescendant(cf =>
                    cf.ByControlType(FlaUI.Core.Definitions.ControlType.MenuItem)
                    .And(cf.ByAutomationId(automationId)));

                if (menuItem == null)
                    throw new Exception($"未找到AutomationId为 {automationId} 的菜单项");

                // 确保菜单项可见和可点击
                try
                {
                    // 如果是子菜单，可能需要先展开父菜单
                    var parent = menuItem.Parent;
                    if (parent != null && parent.ControlType == FlaUI.Core.Definitions.ControlType.MenuItem)
                    {
                        parent.Focus();
                        parent.Click();
                        // 给一个短暂的延时让子菜单展开
                        Thread.Sleep(100);
                    }

                    menuItem.Focus();
                    menuItem.Click();
                }
                catch (Exception ex)
                {
                    throw new Exception($"点击菜单项失败: {ex.Message}");
                }
            }
        }




    }
}