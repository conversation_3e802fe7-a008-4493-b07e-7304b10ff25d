﻿using System;
using System.Diagnostics;
using System.Windows.Automation;

namespace DesktopRpaLib.Actions
{
    class ComboBoxAction
    {



        // 获取所有项
        public string[] GetComboBoxItems(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);


            var itemCondition = new PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.ListItem);
            var items = element.FindAll(TreeScope.Subtree, itemCondition);

            if (items.Count == 0)
                throw new Exception("未找到任何选项");

            string[] itemNames = new string[items.Count];
            for (int i = 0; i < items.Count; i++)
            {
                itemNames[i] = items[i].GetCurrentPropertyValue(AutomationElement.NameProperty).ToString();
            }


            return itemNames;
        }





        // 获取当前选中值
        public string GetCurrentComboBoxValue(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            // 获取当前选中的项
            var selectionPattern = element.GetCurrentPattern(SelectionPattern.Pattern) as SelectionPattern;
            if (selectionPattern == null || selectionPattern.Current.GetSelection().Length == 0)
            {
                throw new Exception("未找到当前选中项");
            }

            var selectedItem = selectionPattern.Current.GetSelection()[0];
            return selectedItem.GetCurrentPropertyValue(AutomationElement.NameProperty).ToString();
        }





        // 设置指定值
        public void SetComboBoxValue(int processId, string automationId, string valueToSelect)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var itemCondition = new PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.ListItem);
            var items = element.FindAll(TreeScope.Subtree, itemCondition);

            foreach (AutomationElement item in items)
            {
                string itemName = item.GetCurrentPropertyValue(AutomationElement.NameProperty).ToString();
                if (itemName == valueToSelect)
                {
                    var selectionItemPattern = (SelectionItemPattern)item.GetCurrentPattern(SelectionItemPattern.Pattern);
                    selectionItemPattern.Select();
                    return;
                }
            }

            throw new Exception("未找到指定的 ComboBox 项");
        }



    }
}
