﻿using System.Web.Http;
using DesktopRpaLib.Response;
using DesktopRpaLib.Actions;
using System;
using System.Runtime.InteropServices;
using System.Text;
using System.Diagnostics;
using System.Threading;
using FlaUI.Core;
using FlaUI.UIA3;
using FlaUI.Core.AutomationElements;
using System.Collections.Generic;




[RoutePrefix("api/exe")] // 定义路由前缀
public class ExeController : ApiController
{


    [DllImport("user32.dll")]
    static extern bool EnumWindows(EnumWindowsProc lpEnumFunc, IntPtr lParam);

    [DllImport("user32.dll")]
    static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

    [DllImport("user32.dll")]
    static extern bool IsWindowVisible(IntPtr hWnd);

    [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
    static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

    private const uint BM_CLICK = 0x00F5;

    delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

    [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
    static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

    [DllImport("user32.dll", SetLastError = true)]
    static extern bool SetForegroundWindow(IntPtr hWnd);

    [DllImport("user32.dll", SetLastError = true)]
    static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, uint dwExtraInfo);

    private const byte VK_MENU = 0x12; // Alt key
    private const byte VK_Y = 0x59;   // Y key
    private const uint KEYEVENTF_KEYUP = 0x0002; // Key up flag





    private readonly ExeAction _exeAction = new ExeAction();



    // 获取所有值
    [Route("getPidsByExeName")]
    public IHttpActionResult GetPidsByExeName([FromUri] string exeName)
    {
        if (string.IsNullOrEmpty(exeName))
        {
            return Json(ResponseResult<string>.Error(msg: "exeName参数不能为空"));
        }

        if (exeName.EndsWith(".exe"))
        {
            return Json(ResponseResult<string>.Error(msg: "不需要.exe后缀"));
        }

        try
        {
            var allValues = _exeAction.GetPidsByExeName(exeName);
            var data = new { ExeName = exeName, AllPids = allValues };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }

    }




    // 根据 PID 获取 exeName 和 主窗口标题
    [Route("getExeNameAndTitle/{processId:int}")]
    public IHttpActionResult GetExeNameAndTitle(int processId)
    {
        try
        {
            var (exeName, exePath, mainWindowTitle) = _exeAction.GetExeInfoByPid(processId);

            var data = new
            {
                ExePath = exePath,           // 完整的文件路径
                ExeName = exeName,           // 只包含文件名 (例如 DiskMark64.exe)
                MainWindowTitle = mainWindowTitle
            };

            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }






    // 杀死指定 PID 的进程
    [Route("killProcessByPid/{processId:int}")]
    public IHttpActionResult PostKillProcessByPid(int processId)
    {
        try
        {
            _exeAction.KillProcessByPid(processId);

            return Json(ResponseResult<object>.Success(msg: $"杀死PID:{processId}成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }



    // 启动指定路径的快捷方式
    [Route("startShortcut")]
    public IHttpActionResult PostStartShortcut([FromUri] string shortcutPath)
    {
        try
        {
            if (string.IsNullOrEmpty(shortcutPath))
            {
                return Json(ResponseResult<string>.Error(msg: "shortcutPath参数不能为空"));
            }

            _exeAction.StartShortcut(shortcutPath);



            return Json(ResponseResult<object>.Success(msg: $"启动成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }

    }






    // 启动指定路径的 exe
    [Route("startExe")]
    public IHttpActionResult PostStartExe([FromUri] string exePath)
    {
        try
        {
            if (string.IsNullOrEmpty(exePath))
            {
                return Json(ResponseResult<string>.Error(msg: "exePath参数不能为空"));
            }

            int pid = _exeAction.StartExe(exePath);
            var data = new { Pid = pid };

            return Json(ResponseResult<object>.Success(msg: $"启动exe成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }

    }




    // 以管理员身份启动指定路径的 exe
    [Route("startExeByAdmin")]
    public IHttpActionResult PostStartExeByAdmin([FromUri] string exePath)
    {
        try
        {
            if (string.IsNullOrEmpty(exePath))
            {
                return Json(ResponseResult<string>.Error(msg: "exePath参数不能为空"));
            }

            int pid = _exeAction.StartExeByAdmin(exePath);
            var data = new { Pid = pid };

            return Json(ResponseResult<object>.Success(msg: $"管理员身份启动exe成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 处理Windows安全中心弹框
    [Route("dealSafeCenterPopUp")]
    public IHttpActionResult PostDealSafeCenterPopUp([FromUri] int dealTime = 10, [FromUri] bool block = false)
    {
        try
        {
            _exeAction.DealSafeCenterPopUp(dealTime, block);

            if (block)
            {
                return Json(ResponseResult<object>.Success(msg: $"异步处理Windows安全中心弹框，等待时间{dealTime}秒"));
            }
            else
            {
                return Json(ResponseResult<object>.Success(msg: $"处理Windows安全中心弹框成功"));
            }
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }






}