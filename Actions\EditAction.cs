﻿using System;
using System.Diagnostics;
using System.Windows.Automation;

namespace DesktopRpaLib.Actions
{
    public class EditAction
    {



        // 编辑框是否可用
        public bool IsEditEnabled(int processId, string automationId)
        {
            return ActionUtil.IsElementEnabled(processId, automationId);
        }



        // 获取编辑框的文本内容
        public string GetEditText(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var valuePattern = element.GetCurrentPattern(ValuePattern.Pattern) as ValuePattern;
            if (valuePattern != null)
            {
                return valuePattern.Current.Value;
            }
            else
            {
                throw new Exception("无法获取编辑框的文本");
            }
        }



        // 设置编辑框的文本内容
        public void SetEditText(int processId, string automationId, string text)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var valuePattern = element.GetCurrentPattern(ValuePattern.Pattern) as ValuePattern;
            if (valuePattern != null)
            {
                valuePattern.SetValue(text);
            }
            else
            {
                throw new Exception("无法设置编辑框的文本");
            }
        }




        // 清空编辑框文本内容
        public void ClearEditText(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var valuePattern = element.GetCurrentPattern(ValuePattern.Pattern) as ValuePattern;
            if (valuePattern != null)
            {
                valuePattern.SetValue(string.Empty);
            }
            else
            {
                throw new Exception("无法清空编辑框的文本");
            }
        }


    }
}
