﻿using System;
using System.Web.Http;
using DesktopRpaLib.Response;
using DesktopRpaLib.Actions;




[RoutePrefix("api/serialPort")]
public class SerialPortController : ApiController
{
    private readonly SerialPortAction _serialPortAction = new SerialPortAction();



    // 获取所有串口号
    [Route("getPortNames")]
    public IHttpActionResult GetPortNames()
    {
        try
        {
            var portNames = _serialPortAction.GetPortNames();
            var data = new { PortNames = portNames };
            return Json(ResponseResult<object>.Success(msg: "获取所有串口号成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 断电
    [Route("powerOff")]
    public IHttpActionResult PostPowerOff([FromUri] string portName, [FromUri] int portNumber)
    {
        if (string.IsNullOrEmpty(portName))
        {
            return Json(ResponseResult<string>.Error(msg: "portName参数不能为空"));
        }

        if (portNumber <= 0)
        {
            return Json(ResponseResult<string>.Error(msg: "portNumber参数无效"));
        }


        try
        {
            _serialPortAction.PowerOff(portName, portNumber);
            return Json(ResponseResult<object>.Success(msg: "断电成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }



    // 上电
    [Route("powerOn")]
    public IHttpActionResult PostPowerOn([FromUri] string portName, [FromUri] int portNumber)
    {
        if (string.IsNullOrEmpty(portName))
        {
            return Json(ResponseResult<string>.Error(msg: "portName参数不能为空"));
        }


        if (portNumber <= 0)
        {
            return Json(ResponseResult<string>.Error(msg: "portNumber参数无效"));
        }


        try
        {
            _serialPortAction.PowerOn(portName, portNumber);
            return Json(ResponseResult<object>.Success(msg: "上电成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }







    // 获取Hub信息
    [Route("getHubInfo")]
    public IHttpActionResult GetHubInfo()
    {
   
        try
        {
            var hubInfo = _serialPortAction.GetHubInfo();
            var data = new { HubInfo = hubInfo };
            return Json(ResponseResult<object>.Success(msg: "获取成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }







}

