[SETTING]
SUPPORT_BOOT = 1
SUPPORT_EXTCODE = 1
SUPPORT_NEW_VER = 1
MPVERSION = Mptool_v7.147.10920_Debug
FLASHTYPE = 16
FLASHNUM = 0
CYCLE = 0
FIXTYPE = 0
SPI_BOOT = 0
OTP_BOOT = 0
FIX_FLASH_NAME = H27TDG8T2D
SCANMODE = 1
SCANLEVEL = 1
HIGHSCANSETTINGFLAG = 0
HIGHSCANIOSDINFO = 0
MFIXPAGECNT = 0
ECC = 5
SPDCAPSEL = 1
SDSPD = 0
AUTOSTARTMP = 0
EVOLVE = 0
REFRESH_MS = 1000
AUTO_START_DIE = 1
FIXPAGECNT = 0
CAPSET = 1
CAPFIXEDMIN = 0
CAPFIXEDMAX = 0
LABEL = SD
SNMODE = 0
SNNUM = 0123456789ABCDEF
MID = 00
OID = 2020
PNM = N/A
PRVN = 2
MDTY = 18
MDTM = 7
FIXDATE = 0
WRITEIMG = 0
ERASEALLBLK = 1
IMGFILEDIR = Test.iso
READONLY = 1
FIXCPCHECK = 11
CHECKSCSC = 0
ALREADYPROD = 0
ENFORCESLCMODE = 0
ENCOPYBACK = 0
FORMAT = 1
BLOCKNUM = 0
PLANENUM = 0
ADDRDECODERBLKNUM = 4
HWREG_EX_EN = 1
IO1234567CTRL0 = 129
IO1234567CTRL1 = 448
IO0CTRL0 = 129
IO0CTRL1 = 448
IO3CTRL0 = 129
IO3CTRL1 = 448
CMDCTRL0 = 129
CMDCTRL1 = 448
CLKCTRL0 = 20
CLKCTRL1 = 16
DELAYLINECTRL = 160
DATABUSSKEWCTRL = 0
PADLDOCFG = 37686472
TESTSNMODE = 0
STARTSN = 0
PORT1TESTSN = 0
PORT2TESTSN = 1
PORT3TESTSN = 2
PORT4TESTSN = 3
PORT5TESTSN = 4
PORT6TESTSN = 5
PORT7TESTSN = 6
PORT8TESTSN = 7
PORT9TESTSN = 8
PORT10TESTSN = 9
PORT11TESTSN = 10
PORT12TESTSN = 11
PORT13TESTSN = 12
PORT14TESTSN = 13
PORT15TESTSN = 14
PORT16TESTSN = 15
PORT17TESTSN = 16
PORT18TESTSN = 17
PORT19TESTSN = 18
PORT20TESTSN = 19
PORT21TESTSN = 20
PORT22TESTSN = 21
PORT23TESTSN = 22
PORT24TESTSN = 23
PORT25TESTSN = 24
PORT26TESTSN = 25
PORT27TESTSN = 26
PORT28TESTSN = 27
PORT29TESTSN = 28
PORT30TESTSN = 29
PORT31TESTSN = 30
PORT32TESTSN = 31
UIPORTNUM = 1
FIXPORTTYPE = 1
FIXPORT1PORTNUM = 255
FIXPORT1PORTIDX = 255
FIXPORT2PORTNUM = 255
FIXPORT2PORTIDX = 255
FIXPORT3PORTNUM = 255
FIXPORT3PORTIDX = 255
FIXPORT4PORTNUM = 255
FIXPORT4PORTIDX = 255
FIXPORT5PORTNUM = 255
FIXPORT5PORTIDX = 255
FIXPORT6PORTNUM = 255
FIXPORT6PORTIDX = 255
FIXPORT7PORTNUM = 255
FIXPORT7PORTIDX = 255
FIXPORT8PORTNUM = 255
FIXPORT8PORTIDX = 255
FIXPORT9PORTNUM = 255
FIXPORT9PORTIDX = 255
FIXPORT10PORTNUM = 255
FIXPORT10PORTIDX = 255
FIXPORT11PORTNUM = 255
FIXPORT11PORTIDX = 255
FIXPORT12PORTNUM = 255
FIXPORT12PORTIDX = 255
FIXPORT13PORTNUM = 255
FIXPORT13PORTIDX = 255
FIXPORT14PORTNUM = 255
FIXPORT14PORTIDX = 255
FIXPORT15PORTNUM = 255
FIXPORT15PORTIDX = 255
FIXPORT16PORTNUM = 255
FIXPORT16PORTIDX = 255
FIXPORT17PORTNUM = 255
FIXPORT17PORTIDX = 255
FIXPORT18PORTNUM = 255
FIXPORT18PORTIDX = 255
FIXPORT19PORTNUM = 255
FIXPORT19PORTIDX = 255
FIXPORT20PORTNUM = 255
FIXPORT20PORTIDX = 255
FIXPORT21PORTNUM = 255
FIXPORT21PORTIDX = 255
FIXPORT22PORTNUM = 255
FIXPORT22PORTIDX = 255
FIXPORT23PORTNUM = 255
FIXPORT23PORTIDX = 255
FIXPORT24PORTNUM = 255
FIXPORT24PORTIDX = 255
FIXPORT25PORTNUM = 255
FIXPORT25PORTIDX = 255
FIXPORT26PORTNUM = 255
FIXPORT26PORTIDX = 255
FIXPORT27PORTNUM = 255
FIXPORT27PORTIDX = 255
FIXPORT28PORTNUM = 255
FIXPORT28PORTIDX = 255
FIXPORT29PORTNUM = 255
FIXPORT29PORTIDX = 255
FIXPORT30PORTNUM = 255
FIXPORT30PORTIDX = 255
FIXPORT31PORTNUM = 255
FIXPORT31PORTIDX = 255
FIXPORT32PORTNUM = 255
FIXPORT32PORTIDX = 255
DECODE_START_REG = 0
DECODE_END_REG = 0
DECODE_TABLE_START1REG = 0
DECODE_TABLE_END1REG = 0
DECODE_TABLE1REG = 0
DECODE_TABLE_START2REG = 0
DECODE_TABLE_END2REG = 0
DECODE_TABLE2REG = 0
DECODE_TABLE_START3REG = 0
DECODE_TABLE_END3REG = 0
DECODE_TABLE3REG = 0
DECODE_TABLE_START4REG = 0
DECODE_TABLE_END4REG = 0
DECODE_TABLE4REG = 0
DECODE_TABLE_START5REG = 0
DECODE_TABLE_END5REG = 0
DECODE_TABLE5REG = 0
DECODE_TABLE_START6REG = 0
DECODE_TABLE_END6REG = 0
DECODE_TABLE6REG = 0
DECODE_TABLE_START7REG = 0
DECODE_TABLE_END7REG = 0
DECODE_TABLE7REG = 0
DECODE_TABLE_START8REG = 0
DECODE_TABLE_END8REG = 0
DECODE_TABLE8REG = 0
DECODE_TABLE_START9REG = 0
DECODE_TABLE_END9REG = 0
DECODE_TABLE9REG = 0
DECODE_TABLE_START10REG = 0
DECODE_TABLE_END10REG = 0
DECODE_TABLE10REG = 0
DECODE_TABLE_START11REG = 0
DECODE_TABLE_END11REG = 0
DECODE_TABLE11REG = 0
DECODE_TABLE_START12REG = 0
DECODE_TABLE_END12REG = 0
DECODE_TABLE12REG = 0
DECODE_TABLE_START13REG = 0
DECODE_TABLE_END13REG = 0
DECODE_TABLE13REG = 0
DECODE_TABLE_START14REG = 0
DECODE_TABLE_END14REG = 0
DECODE_TABLE14REG = 0
DECODE_TABLE_START15REG = 0
DECODE_TABLE_END15REG = 0
DECODE_TABLE15REG = 0
DECODE_TABLE_START16REG = 0
DECODE_TABLE_END16REG = 0
DECODE_TABLE16REG = 0
DECODE_TABLE_START17REG = 0
DECODE_TABLE_END17REG = 0
DECODE_TABLE17REG = 0
DECODE_TABLE_START18REG = 0
DECODE_TABLE_END18REG = 0
DECODE_TABLE18REG = 0
DECODE_TABLE_START19REG = 0
DECODE_TABLE_END19REG = 0
DECODE_TABLE19REG = 0
DECODE_TABLE_START20REG = 0
DECODE_TABLE_END20REG = 0
DECODE_TABLE20REG = 0
DECODE_TABLE_START21REG = 0
DECODE_TABLE_END21REG = 0
DECODE_TABLE21REG = 0
DECODE_TABLE_START22REG = 0
DECODE_TABLE_END22REG = 0
DECODE_TABLE22REG = 0
DECODE_TABLE_START23REG = 0
DECODE_TABLE_END23REG = 0
DECODE_TABLE23REG = 0
DECODE_TABLE_START24REG = 0
DECODE_TABLE_END24REG = 0
DECODE_TABLE24REG = 0
DECODE_TABLE_START25REG = 0
DECODE_TABLE_END25REG = 0
DECODE_TABLE25REG = 0
DECODE_TABLE_START26REG = 0
DECODE_TABLE_END26REG = 0
DECODE_TABLE26REG = 0
DECODE_TABLE_START27REG = 0
DECODE_TABLE_END27REG = 0
DECODE_TABLE27REG = 0
DECODE_TABLE_START28REG = 0
DECODE_TABLE_END28REG = 0
DECODE_TABLE28REG = 0
DECODE_TABLE_START29REG = 0
DECODE_TABLE_END29REG = 0
DECODE_TABLE29REG = 0
DECODE_TABLE_START30REG = 0
DECODE_TABLE_END30REG = 0
DECODE_TABLE30REG = 0
DECODE_TABLE_START31REG = 0
DECODE_TABLE_END31REG = 0
DECODE_TABLE31REG = 0
DECODE_TABLE_START32REG = 0
DECODE_TABLE_END32REG = 0
DECODE_TABLE32REG = 0
DECODE_RUN_IDX = 0
DIESTYPE = 0
ONLYFORMATDISK = 0
UIUPTIMEOUT = 5000
ORCARDREADER = 1
FIX_HUB0 = 0000000000010201
FIX_HUB1 = 0000000000030201
FIX_HUB2 = 0000000000000000
FIX_HUB3 = 0000000000000000
FIX_HUB4 = 0000000000000000
FIX_HUB5 = 0000000000000000
FIX_HUB6 = 0000000000000000
FIX_HUB7 = 0000000000000000
OTP_SIZE = 16384
LIBOWEN_KEHU = 0
SPI_ID0 = 0x14EF
SPI_ID1 = 0x14C2
SPI_ID2 = 0x12C8
SPI_ID3 = 0x11C8
SPI_ID4 = 0x1437
SPI_ID5 = 0xFFEF
SPI_ID6 = 0x05C8
SPI_ID7 = 0x10C8
SPI_ID8 = 0x12C2
SPI_ID9 = 0x05EF
SPI_ID10 = 0x1185
SPI_ID11 = 0xFFFF
ENABLE_ENCRYPTION = 0
ENCRYPYION_SIZE_TPYE = 1
ENCRYPYION_SIZE_CACP = 50
ENCRYPYION_PERCENTAGE = 100
INTEGRITY = 0
VID = 31C0
PID = 1234
CUSTOMER_INFO = Generic
PRODUCT_INFO = Mass Storage
DEVICETYPE = 0
ENCRTYTION_PASSWORD = 123456
AUTORUNFILENAME = 100MBAR.iso
CAPARITH = 0
ORDINARY_SIZE_Capa = 0
ENCRYPYION_SIZE_Capa = 32
ORDINARY_PERCENTAGE = 0
DOUSPDCAPSEL = 1
ENABLEDDRMODE = 1
B4PCLR = 255
B2PCLR = 16711935
B1PCLR = 4227327
P1PCLR = 65535
SLCCLR = 16711680
ONLYREADFILENAME = dianying02.IMA
OPENPRODUCTSETTING = 0
OPENDEVICETYPESETTING = 0
OMZSPEEDS = 1
POWERLDO = 0
PARAMPULLUPSEL = 8
PARAMFRDNCFG = 104
PROJECT_ID_NO_0 = 0
PROJECT_ID_NO_1 = 1
PROJECT_ID_NO_2 = 1
PROJECT_ID_NO_3 = 1
PROJECT_ID_NO_4 = 1
PROJECT_ID_NO_5 = 1
PROJECT_ID_NO_6 = 1
PROJECT_ID_NO_7 = 1
PROJECT_ID_NO_8 = 1
PROJECT_ID_NO_9 = 1
PROJECT_ID_NO_10 = 1
PROJECT_ID_NO_11 = 1
PROJECT_ID_NO_12 = 1
PROJECT_ID_NO_13 = 1
PROJECT_ID_NO_14 = 1
PROJECT_ID_NO_15 = 1
PROJECT_ID_NO_16 = 1
PROJECT_ID_NO_17 = 1
PROJECT_ID_NO_18 = 1
PROJECT_ID_NO_19 = 1
PROJECT_ID_NO_20 = 1
PROJECT_ID_NO_21 = 1
PROJECT_ID_NO_22 = 1
PROJECT_ID_NO_23 = 1
PROJECT_ID_NO_24 = 1
PROJECT_ID_NO_25 = 1
DELETE_HIGHT_ECCPAGE = 1
ADJ_SLC_ARRAY = 1
SINGLE_ARRAY = 0
TESTCAPACITYSETPLANEMODEL = 3
CHECKOSCCLK_2981 = 1
SLEEPENABLE = 1
SLEEPJUMPBOOT = 0
WAKEUP_CFG = 720179
SLEEPCNT = 983040
SD30ENABLE = 1
SDR12 = 1
SDR25 = 1
SDR50 = 1
SDR104 = 1
DDR50 = 1
CARDREADY_2981 = 1
TESTLEVEL = 11
PCLOWMEMERY1G = 0
HUBPORTSORTTYPE = 2
HUBPORTROW = 4
HUBPORTCOLUMN = 8
HUBPORTCNTINDEX0 = -2147483648
HUBPORTCNTINDEX1 = -2147483647
HUBPORTCNTINDEX2 = -2147483646
HUBPORTCNTINDEX3 = -2147483645
HUBPORTCNTINDEX4 = -2147483644
HUBPORTCNTINDEX5 = -2147483643
HUBPORTCNTINDEX6 = -2147483642
HUBPORTCNTINDEX7 = -2147483641
HUBPORTCNTINDEX8 = -2147483640
HUBPORTCNTINDEX9 = -2147483639
HUBPORTCNTINDEX10 = -2147483638
HUBPORTCNTINDEX11 = -2147483637
HUBPORTCNTINDEX12 = -2147483636
HUBPORTCNTINDEX13 = -2147483635
HUBPORTCNTINDEX14 = -2147483634
HUBPORTCNTINDEX15 = -2147483633
HUBPORTCNTINDEX16 = -2147483632
HUBPORTCNTINDEX17 = -2147483631
HUBPORTCNTINDEX18 = -2147483630
HUBPORTCNTINDEX19 = -2147483629
HUBPORTCNTINDEX20 = -2147483628
HUBPORTCNTINDEX21 = -2147483627
HUBPORTCNTINDEX22 = -2147483626
HUBPORTCNTINDEX23 = -2147483625
HUBPORTCNTINDEX24 = -2147483624
HUBPORTCNTINDEX25 = -2147483623
HUBPORTCNTINDEX26 = -2147483622
HUBPORTCNTINDEX27 = -2147483621
HUBPORTCNTINDEX28 = -2147483620
HUBPORTCNTINDEX29 = -2147483619
HUBPORTCNTINDEX30 = -2147483618
HUBPORTCNTINDEX31 = -2147483617
MUBIAOCLK = 113
ONLYREADFILEREADYSIZE = 256
HSF_Designated_Path = 0
CACHEINFONUM_0 = 10
CACHEINFONUM_1 = 20
CACHEINFONUM_2 = 30
CACHEINFONUM_3 = 40
CACHEINFONUM_4 = 50
CACHEINFONUM_5 = 60
CACHEINFONUM_6 = 70
CACHEINFONUM_7 = 80
CACHEINFOCOLOR_0 = 16744448
CACHEINFOCOLOR_1 = 8421376
CACHEINFOCOLOR_2 = 4259584
CACHEINFOCOLOR_3 = 16776960
CACHEINFOCOLOR_4 = 16711680
CACHEINFOCOLOR_5 = 16711808
CACHEINFOCOLOR_6 = 16711935
CACHEINFOCOLOR_7 = 255
RESCUECARDMODEL = 0
DOWNBINDOWNSPEED = 1
CalculateThePercentage = 70
RejectionPercentage = 50
TL_CREATEFILE_SPACETYPE = 0
TL_CREATEFILE_SPACESIZE = 10
TL_CREATEFILE_FILESIZE = 5
TL_CREATEFILE_CHECKDATA = 1
SHOWSLCCAPACITYLEVEL = 0
SD_REGINFO_CID0 = 53030389
SD_REGINFO_CID1 = 38323144
SD_REGINFO_CID2 = 29301910
SD_REGINFO_CID3 = B7A10012
SD_REGINFO_CID_MDT_AUTO = 1
SERIALPORT_EN = 0
SERIALPORT_COM = 1
SERIALPORT_RAUDRATE = 9600
SERIALPORT_DATA = 8
SERIALPORT_VERIFY = 0
SERIALPORT_STOP = 0
BIN1-USBCAPSEL = 120800
BIN1-USBFIXCAP = 1
BIN1-USBBINCLR = 8421631
BIN1-USBFIXEDBIN = 0
BIN2-USBCAPSEL = 60400
BIN2-USBFIXCAP = 1
BIN2-USBBINCLR = 4194432
BIN2-USBFIXEDBIN = 0
BIN3-USBCAPSEL = 30200
BIN3-USBFIXCAP = 1
BIN3-USBBINCLR = 16711935
BIN3-USBFIXEDBIN = 0
BIN4-USBCAPSEL = 15200
BIN4-USBFIXCAP = 1
BIN4-USBBINCLR = 16744576
BIN4-USBFIXEDBIN = 0
BIN5-USBCAPSEL = 7680
BIN5-USBFIXCAP = 1
BIN5-USBBINCLR = 33023
BIN5-USBFIXEDBIN = 0
BIN6-USBCAPSEL = 3840
BIN6-USBFIXCAP = 1
BIN6-USBBINCLR = 16776960
BIN6-USBFIXEDBIN = 0
BIN7-USBCAPSEL = 1920
BIN7-USBFIXCAP = 1
BIN7-USBBINCLR = 32768
BIN7-USBFIXEDBIN = 0
BIN8-USBCAPSEL = 960
BIN8-USBFIXCAP = 1
BIN8-USBBINCLR = 65280
BIN8-USBFIXEDBIN = 0
BIN9-USBCAPSEL = 480
BIN9-USBFIXCAP = 1
BIN9-USBBINCLR = 8454016
BIN9-USBFIXEDBIN = 0
BIN10-USBCAPSEL = 240
BIN10-USBFIXCAP = 1
BIN10-USBBINCLR = 32896
BIN10-USBFIXEDBIN = 0
BIN11-USBCAPSEL = 120
BIN11-USBFIXCAP = 1
BIN11-USBBINCLR = 33023
BIN11-USBFIXEDBIN = 0
BIN12-USBCAPSEL = 60
BIN12-USBFIXCAP = 1
BIN12-USBBINCLR = 8388863
BIN12-USBFIXEDBIN = 0
BIN13-USBCAPSEL = 0
BIN13-USBFIXCAP = 0
BIN13-USBBINCLR = 16777215
BIN13-USBFIXEDBIN = 0
BIN14-USBCAPSEL = 0
BIN14-USBFIXCAP = 0
BIN14-USBBINCLR = 16777215
BIN14-USBFIXEDBIN = 0
BIN15-USBCAPSEL = 56
BIN15-USBFIXCAP = 0
BIN15-USBBINCLR = 8388863
BIN15-USBFIXEDBIN = 0
BIN16-USBCAPSEL = 32
BIN16-USBFIXCAP = 0
BIN16-USBBINCLR = 16711808
BIN16-USBFIXEDBIN = 0
BIN1-SDCAPSEL = 119276
BIN1-SDFIXCAP = 1
BIN1-SDBINCLR = 8388863
BIN1-SDFIXEDBIN = 0
BIN2-SDCAPSEL = 59638
BIN2-SDFIXCAP = 1
BIN2-SDBINCLR = 16711935
BIN2-SDFIXEDBIN = 0
BIN3-SDCAPSEL = 29818
BIN3-SDFIXCAP = 1
BIN3-SDBINCLR = 16711808
BIN3-SDFIXEDBIN = 0
BIN4-SDCAPSEL = 14910
BIN4-SDFIXCAP = 1
BIN4-SDBINCLR = 16711680
BIN4-SDFIXEDBIN = 0
BIN5-SDCAPSEL = 7680
BIN5-SDFIXCAP = 1
BIN5-SDBINCLR = 16744448
BIN5-SDFIXEDBIN = 0
BIN6-SDCAPSEL = 3840
BIN6-SDFIXCAP = 1
BIN6-SDBINCLR = 16776960
BIN6-SDFIXEDBIN = 0
BIN7-SDCAPSEL = 1920
BIN7-SDFIXCAP = 1
BIN7-SDBINCLR = 32768
BIN7-SDFIXEDBIN = 0
BIN8-SDCAPSEL = 960
BIN8-SDFIXCAP = 1
BIN8-SDBINCLR = 4259584
BIN8-SDFIXEDBIN = 0
BIN9-SDCAPSEL = 480
BIN9-SDFIXCAP = 1
BIN9-SDBINCLR = 8454016
BIN9-SDFIXEDBIN = 0
BIN10-SDCAPSEL = 240
BIN10-SDFIXCAP = 1
BIN10-SDBINCLR = 32896
BIN10-SDFIXEDBIN = 0
BIN11-SDCAPSEL = 120
BIN11-SDFIXCAP = 1
BIN11-SDBINCLR = 33023
BIN11-SDFIXEDBIN = 0
BIN12-SDCAPSEL = 60
BIN12-SDFIXCAP = 1
BIN12-SDBINCLR = 8388863
BIN12-SDFIXEDBIN = 0
BIN13-SDCAPSEL = 0
BIN13-SDFIXCAP = 0
BIN13-SDBINCLR = 16777215
BIN13-SDFIXEDBIN = 0
BIN14-SDCAPSEL = 0
BIN14-SDFIXCAP = 0
BIN14-SDBINCLR = 16777215
BIN14-SDFIXEDBIN = 0
BIN15-SDCAPSEL = 56
BIN15-SDFIXCAP = 0
BIN15-SDBINCLR = 8388863
BIN15-SDFIXEDBIN = 0
BIN16-SDCAPSEL = 32
BIN16-SDFIXCAP = 0
BIN16-SDBINCLR = 16711935
BIN16-SDFIXEDBIN = 0
8581_SYSCLK_SEL = 0
8581_SRCCLK_SEL = 3
8581_M_RDDLY = 0
M_IO_CFG3 = 0
M_IO_CFG4 = 0
M_NFCCLKDIV = 3
M_NFCDCLKDIV = 0
M_INTFMODE = 1
M_FLASHTIMING = 32
M_WDDLY = 112
M_WDATGAP = 4
DEBUG_ALLSHOW = 0
SNNUM0 = 11
SNNUM1 = 22
SNNUM2 = 33
SNNUM3 = 44
SNNUM4 = 55
SNNUM5 = 66
SNNUM6 = 77
SNNUM7 = 0
SNNUM8 = 0
SNNUM9 = 0
SNNUM10 = 0
SNNUM11 = 0
SNNUM12 = 0
SNNUM13 = 0
SNNUM14 = 0
SNNUM15 = 0
M_PORTCNT = 32
M_CHECKAUTO = 0
M_PORT1 = 1
M_PORT2 = 2
M_PORT3 = 3
M_PORT4 = 4
M_PORT5 = 5
M_PORT6 = 6
M_PORT7 = 7
M_PORT8 = 8
M_PORT9 = 9
M_PORT10 = 10
M_PORT11 = 11
M_PORT12 = 12
M_PORT13 = 13
M_PORT14 = 14
M_PORT15 = 15
M_PORT16 = 16
M_PORT17 = 17
M_PORT18 = 18
M_PORT19 = 19
M_PORT20 = 20
M_PORT21 = 21
M_PORT22 = 22
M_PORT23 = 23
M_PORT24 = 24
M_PORT25 = 25
M_PORT26 = 26
M_PORT27 = 27
M_PORT28 = 28
M_PORT29 = 29
M_PORT30 = 30
M_PORT31 = 31
M_PORT32 = 32
MUBIAOCLKCFGTYPE = 0
MUBIAOCLK_0 = 110
MUBIAOCLK_1 = 113
MUBIAOCLK_2 = 0
MUBIAOCLK_3 = 0
MUBIAOCLK_4 = 0
MUBIAOCLK_5 = 0
MUBIAOCLK_6 = 1
MUBIAOCLK_7 = 70
EliminateBlockTable0 = FFFFFFFF
EliminateBlockTable1 = FFFFFFFF
EliminateBlockTable2 = FFFFFFFF
EliminateBlockTable3 = FFFFFFFF
EliminateBlockTable4 = FFFFFFFF
EliminateBlockTable5 = FFFFFFFF
EliminateBlockTable6 = FFFFFFFF
EliminateBlockTable7 = FFFFFFFF
MODE_MAXIMUM_SLC = 30
MODE_MAXIMUM_TLC = 60
UNUSUAL_MODE_THRESHOLD_SLC = 10
UNUSUAL_MODE_THRESHOLD_TLC = 10
UNUSUAL_MODE_THRESHOLD_CNT_SLC = 10
UNUSUAL_MODE_THRESHOLD_CNT_TLC = 20
PROCESS_OF_ELIMINATING_UNSTABLE_SLC_BLOCK_ENABLE = 0
PROCESS_OF_ELIMINATING_UNSTABLE_SLC_BLOCK_NUM = 20
PROCESS_OF_ELIMINATING_UNSTABLE_SLC_BLOCK_TEST_CASE = 10
PROCESS_OF_ELIMINATING_UNSTABLE_SLC_BLOCK_D_VALUE = 20
DQS_DRI_THD_8381 = 2B
DQS_DLY_8381 = 31F
SYSCLK_CLK_8381 = 1
FC_CLK_8381 = 3
FC_SMP_8381 = 2
DMAECCPRINT = 0
USBDEVICETYPE = 0
SPECIALBADCOLUMSWITCH = 1
DDR_TEST = 1
ElimPageECC_0 = 0
ElimPageECC_1 = 0
ElimPageECC_2 = 0
ElimPageECC_3 = 0
ElimPageECC_4 = 0
ElimPageECC_5 = 0
ElimPageECC_6 = 0
ElimPageECC_7 = 1
ElimPageECC_8 = 1
ElimPageECC_9 = 0
ElimPageECC_10 = 0
SHOWDRVLETTER = 0
M_PAGEPER = 0
CapacitySize = 4
FlashType_00 = TC58LJGAT25
CapacityVal_00 = 128
CapacityUnit_00 = GB
Master_00 = 288X-
CStart_00 = 192
CEnd_00 = 200
PlaneMode_00 = 77
Group_00 = 3
FlashType_01 = TC58LJGAT25
CapacityVal_01 = 128
CapacityUnit_01 = GB
Master_01 = 288X-
CStart_01 = 192
CEnd_01 = 200
PlaneMode_01 = 77
Group_01 = 5
FlashType_02 = TC58LJGAT25
CapacityVal_02 = 128
CapacityUnit_02 = GB
Master_02 = 288X-
CStart_02 = 192
CEnd_02 = 200
PlaneMode_02 = 77
Group_02 = 3
FlashType_03 = TC58LJGAT25
CapacityVal_03 = 128
CapacityUnit_03 = GB
Master_03 = 288X-
CStart_03 = 192
CEnd_03 = 200
PlaneMode_03 = 77
Group_03 = 2
XCFGI(312) = 1
XCFGI(327:320) = 3
XCFGI(335:328) = 71
XCFGI(276:272) = 0
XCFGI(482:480) = 0
XCFGI(486:484) = 0
ShowVersionAll = 1
SUPER_USER_SHOW_ALL_ID = 1
ShowCtmNum = 0
AUTHORIZEDMACHINE = 0
ECC_ADJ_1D_2983_Page = 130
ECC_ADJ_2D_2983_Page = 130
ECC_ADJ_4D_2983_Page = 130
PASSWORD = 
FIXPORT1HUBPATH = 
FIXPORT2HUBPATH = 
FIXPORT3HUBPATH = 
FIXPORT4HUBPATH = 
FIXPORT5HUBPATH = 
FIXPORT6HUBPATH = 
FIXPORT7HUBPATH = 
FIXPORT8HUBPATH = 
FIXPORT9HUBPATH = 
FIXPORT10HUBPATH = 
FIXPORT11HUBPATH = 
FIXPORT12HUBPATH = 
FIXPORT13HUBPATH = 
FIXPORT14HUBPATH = 
FIXPORT15HUBPATH = 
FIXPORT16HUBPATH = 
FIXPORT17HUBPATH = 
FIXPORT18HUBPATH = 
FIXPORT19HUBPATH = 
FIXPORT20HUBPATH = 
FIXPORT21HUBPATH = 
FIXPORT22HUBPATH = 
FIXPORT23HUBPATH = 
FIXPORT24HUBPATH = 
FIXPORT25HUBPATH = 
FIXPORT26HUBPATH = 
FIXPORT27HUBPATH = 
FIXPORT28HUBPATH = 
FIXPORT29HUBPATH = 
FIXPORT30HUBPATH = 
FIXPORT31HUBPATH = 
FIXPORT32HUBPATH = 
VOLUMELABLE = 
SERIALNUMBERFILEPATH = 
CustomerNum = 
M_DEVELOP = 3
FAULT_TOLERANT_ECC_CNT = 20
OPTPRO = 7
M_SCANPRICNT = 10
SupplierInfo = Abcd02
SuppDeviceInfo = Abcd Storage
SPEEDSHOW_ENABLE = 1
SD_INFO_HIK_ID_0 = 12345678
SD_INFO_TF_NAND_ID_0 = AA55AA55
SD_INFO_HIK_MPT_TIME_0 = 101
SD_INFO_HIK_NPT_TIME_AUTO_0 = 1
SD_INFO_HIK_MPT_NUM_0 = 87654321
SD_INFO_HIK_WAFER_TYPE_0 = 0
SD_INFO_MP_INFO_0 = 101
SD_INFO_MP_INFO_AUTO_0 = 1
SD_INFO_ORI_BADBLK_CNT_0 = 0
SD_INFO_SLCDATABLK_0 = FFFFFFFF
SD_INFO_SLCDATABLK_AUTO_0 = 1
SD_INFO_TLCDATABLK_0 = FFFFFFFF
SD_INFO_TLCDATABLK_AUTO_0 = 1
SD_INFO_HIK_ID_1 = 12345678
SD_INFO_TF_NAND_ID_1 = AA55AA55
SD_INFO_HIK_MPT_TIME_1 = 101
SD_INFO_HIK_NPT_TIME_AUTO_1 = 1
SD_INFO_HIK_MPT_NUM_1 = 87654321
SD_INFO_HIK_WAFER_TYPE_1 = 0
SD_INFO_MP_INFO_1 = 101
SD_INFO_MP_INFO_AUTO_1 = 1
SD_INFO_ORI_BADBLK_CNT_1 = 0
SD_INFO_SLCDATABLK_1 = FFFFFFFF
SD_INFO_SLCDATABLK_AUTO_1 = 1
SD_INFO_TLCDATABLK_1 = FFFFFFFF
SD_INFO_TLCDATABLK_AUTO_1 = 1
SD_INFO_HIK_ID_2 = 12345678
SD_INFO_TF_NAND_ID_2 = AA55AA55
SD_INFO_HIK_MPT_TIME_2 = 101
SD_INFO_HIK_NPT_TIME_AUTO_2 = 1
SD_INFO_HIK_MPT_NUM_2 = 87654321
SD_INFO_HIK_WAFER_TYPE_2 = 0
SD_INFO_MP_INFO_2 = 101
SD_INFO_MP_INFO_AUTO_2 = 1
SD_INFO_ORI_BADBLK_CNT_2 = 0
SD_INFO_SLCDATABLK_2 = FFFFFFFF
SD_INFO_SLCDATABLK_AUTO_2 = 1
SD_INFO_TLCDATABLK_2 = FFFFFFFF
SD_INFO_TLCDATABLK_AUTO_2 = 1
SD_INFO_HIK_ID_3 = 12345678
SD_INFO_TF_NAND_ID_3 = AA55AA55
SD_INFO_HIK_MPT_TIME_3 = 101
SD_INFO_HIK_NPT_TIME_AUTO_3 = 1
SD_INFO_HIK_MPT_NUM_3 = 87654321
SD_INFO_HIK_WAFER_TYPE_3 = 0
SD_INFO_MP_INFO_3 = 101
SD_INFO_MP_INFO_AUTO_3 = 1
SD_INFO_ORI_BADBLK_CNT_3 = 0
SD_INFO_SLCDATABLK_3 = FFFFFFFF
SD_INFO_SLCDATABLK_AUTO_3 = 1
SD_INFO_TLCDATABLK_3 = FFFFFFFF
SD_INFO_TLCDATABLK_AUTO_3 = 1
ChipsNumSet = 1
HALFDECODESCAN = 1
MyAutoRunIsoFileDir = 
ChipsNumSet = 1
EnableScanPlanes = 0
DECODE_TABLE_START0REG = 0
DECODE_TABLE_END0REG = 0
DECODE_TABLE0REG = 0
FIX_HUB_U3_0 = 0000000000000000
FIX_HUB_U3_1 = 0000000000000000
FIX_HUB_U3_2 = 0000000000000000
FIX_HUB_U3_3 = 0000000000000000
FIX_HUB_U3_4 = 0000000000000000
FIX_HUB_U3_5 = 0000000000000000
FIX_HUB_U3_6 = 0000000000000000
FIX_HUB_U3_7 = 0000000000000000
SD_REGINFO_CID_PNM_IDX = 5
SD_REGINFO_CID_PNM_STR = 
SD_REGINFO_CID_REVE_PNM = 0
isCreateDiskFile = 1
MyFuncDiskFileDir = 
ABTEST_FLAG = 0
FilesystemofFuncDisk = 0
