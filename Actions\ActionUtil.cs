﻿using System;
using System.Windows.Automation;
using System.Diagnostics;
using System.Collections.Generic;



namespace DesktopRpaLib.Actions
{
    public static class ActionUtil
    {

        // 获取目标进程的主窗口 AutomationElement
        public static AutomationElement GetMainWindow(int processId)
        {
            Process process = Process.GetProcessById(processId);
            return process?.MainWindowHandle == IntPtr.Zero ? null : AutomationElement.FromHandle(process.MainWindowHandle);
        }



        // 查找控件
        public static AutomationElement FindElementByAutomationId(AutomationElement rootElement, string automationId)
        {
            var condition = new PropertyCondition(AutomationElement.AutomationIdProperty, automationId);
            return rootElement.FindFirst(TreeScope.Descendants, condition);
        }




        // 查找控件通过名称
        public static AutomationElement FindElementByName(AutomationElement rootElement, string name)
        {
            var condition = new PropertyCondition(AutomationElement.NameProperty, name);
            return rootElement.FindFirst(TreeScope.Descendants, condition);
        }





        // 获取元素
        public static AutomationElement GetElement(int processId, string automationId)
        {
            var mainWindow = GetMainWindow(processId)
                ?? throw new Exception("无法找到主窗口");

            var element = FindElementByAutomationId(mainWindow, automationId)
                ?? throw new Exception("未找到元素");

            return element;
        }



        // 是否可用
        public static bool IsElementEnabled(int processId, string automationId)
        {
            var element = GetElement(processId, automationId);
            return (bool)element.GetCurrentPropertyValue(AutomationElement.IsEnabledProperty);
        }




    }
}
