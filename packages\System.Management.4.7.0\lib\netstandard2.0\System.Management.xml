﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Management</name>
  </assembly>
  <members>
    <member name="T:System.Management.AuthenticationLevel">
      <summary>Describes the authentication level to be used to connect to WMI. This is used for the COM connection to WMI.</summary>
    </member>
    <member name="F:System.Management.AuthenticationLevel.Call">
      <summary>Call-level COM authentication.</summary>
    </member>
    <member name="F:System.Management.AuthenticationLevel.Connect">
      <summary>Connect-level COM authentication.</summary>
    </member>
    <member name="F:System.Management.AuthenticationLevel.Default">
      <summary>The default COM authentication level. WMI uses the default Windows Authentication setting.</summary>
    </member>
    <member name="F:System.Management.AuthenticationLevel.None">
      <summary>No COM authentication.</summary>
    </member>
    <member name="F:System.Management.AuthenticationLevel.Packet">
      <summary>Packet-level COM authentication.</summary>
    </member>
    <member name="F:System.Management.AuthenticationLevel.PacketIntegrity">
      <summary>Packet Integrity-level COM authentication.</summary>
    </member>
    <member name="F:System.Management.AuthenticationLevel.PacketPrivacy">
      <summary>Packet Privacy-level COM authentication.</summary>
    </member>
    <member name="F:System.Management.AuthenticationLevel.Unchanged">
      <summary>Authentication level should remain as it was before.</summary>
    </member>
    <member name="T:System.Management.CimType">
      <summary>Describes the possible CIM types for properties, qualifiers, or method parameters.</summary>
    </member>
    <member name="F:System.Management.CimType.Boolean">
      <summary>A Boolean. This value maps to the <see cref="T:System.Boolean" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.Char16">
      <summary>A 16-bit character. This value maps to the <see cref="T:System.Char" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.DateTime">
      <summary>A date or time value, represented in a string in DMTF date/time format: yyyymmddHHMMSS.mmmmmmsUUU, where yyyymmdd is the date in year/month/day; HHMMSS is the time in hours/minutes/seconds; mmmmmm is the number of microseconds in 6 digits; and sUUU is a sign (+ or -) and a 3-digit UTC offset. This value maps to the <see cref="T:System.DateTime" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.None">
      <summary>A null value.</summary>
    </member>
    <member name="F:System.Management.CimType.Object">
      <summary>An embedded object. Note that embedded objects differ from references in that the embedded object does not have a path and its lifetime is identical to the lifetime of the containing object. This value maps to the <see cref="T:System.Object" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.Real32">
      <summary>A floating-point 32-bit number. This value maps to the <see cref="T:System.Single" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.Real64">
      <summary>A floating point 64-bit number. This value maps to the <see cref="T:System.Double" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.Reference">
      <summary>A reference to another object. This is represented by a string containing the path to the referenced object. This value maps to the <see cref="T:System.Int16" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.SInt16">
      <summary>A signed 16-bit integer. This value maps to the <see cref="T:System.Int16" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.SInt32">
      <summary>A signed 32-bit integer. This value maps to the <see cref="T:System.Int32" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.SInt64">
      <summary>A signed 64-bit integer. This value maps to the <see cref="T:System.Int64" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.SInt8">
      <summary>A signed 8-bit integer. This value maps to the <see cref="T:System.SByte" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.String">
      <summary>A string. This value maps to the <see cref="T:System.String" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.UInt16">
      <summary>An unsigned 16-bit integer. This value maps to the <see cref="T:System.UInt16" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.UInt32">
      <summary>An unsigned 32-bit integer. This value maps to the <see cref="T:System.UInt32" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.UInt64">
      <summary>An unsigned 64-bit integer. This value maps to the <see cref="T:System.UInt64" /> type.</summary>
    </member>
    <member name="F:System.Management.CimType.UInt8">
      <summary>An unsigned 8-bit integer. This value maps to the <see cref="T:System.Byte" /> type.</summary>
    </member>
    <member name="T:System.Management.CodeLanguage">
      <summary>Defines the languages supported by the code generator.</summary>
    </member>
    <member name="F:System.Management.CodeLanguage.CSharp">
      <summary>A value for generating C# code.</summary>
    </member>
    <member name="F:System.Management.CodeLanguage.JScript">
      <summary>A value for generating JScript code.</summary>
    </member>
    <member name="F:System.Management.CodeLanguage.Mcpp">
      <summary>A value for generating managed C++ code.</summary>
    </member>
    <member name="F:System.Management.CodeLanguage.VB">
      <summary>A value for generating Visual Basic code.</summary>
    </member>
    <member name="F:System.Management.CodeLanguage.VJSharp">
      <summary>A value for generating Visual J# code.</summary>
    </member>
    <member name="T:System.Management.ComparisonSettings">
      <summary>Describes the object comparison modes that can be used with <see cref="M:System.Management.ManagementBaseObject.CompareTo(System.Management.ManagementBaseObject,System.Management.ComparisonSettings)" />. Note that these values may be combined.</summary>
    </member>
    <member name="F:System.Management.ComparisonSettings.IgnoreCase">
      <summary>A mode that compares string values in a case-insensitive manner. This applies to strings and to qualifier values. Property and qualifier names are always compared in a case-insensitive manner whether this flag is specified or not. Value: 16.</summary>
    </member>
    <member name="F:System.Management.ComparisonSettings.IgnoreClass">
      <summary>A mode that assumes that the objects being compared are instances of the same class. Consequently, this value causes comparison of instance-related information only. Use this flag to optimize performance. If the objects are not of the same class, the results are undefined. Value: 8.</summary>
    </member>
    <member name="F:System.Management.ComparisonSettings.IgnoreDefaultValues">
      <summary>A mode that ignores the default values of properties. This value is only meaningful when comparing classes. Value: 4.</summary>
    </member>
    <member name="F:System.Management.ComparisonSettings.IgnoreFlavor">
      <summary>A mode that ignores qualifier flavors. This flag still takes qualifier values into account, but ignores flavor distinctions such as propagation rules and override restrictions. Value: 32.</summary>
    </member>
    <member name="F:System.Management.ComparisonSettings.IgnoreObjectSource">
      <summary>A mode that ignores the source of the objects, namely the server and the namespace they came from, in comparison to other objects. Value: 2.</summary>
    </member>
    <member name="F:System.Management.ComparisonSettings.IgnoreQualifiers">
      <summary>A mode that compares the objects, ignoring qualifiers. Value: 1.</summary>
    </member>
    <member name="F:System.Management.ComparisonSettings.IncludeAll">
      <summary>A mode that compares all elements of the compared objects. Value: 0.</summary>
    </member>
    <member name="T:System.Management.CompletedEventArgs">
      <summary>Holds event data for the <see cref="E:System.Management.ManagementOperationObserver.Completed" /> event.</summary>
    </member>
    <member name="P:System.Management.CompletedEventArgs.Status">
      <summary>Gets the completion status of the operation.</summary>
      <returns>One of the enumeration values that indicates the completion status of the operation.</returns>
    </member>
    <member name="P:System.Management.CompletedEventArgs.StatusObject">
      <summary>Gets additional status information within a WMI object. This may be <see langword="null" />.</summary>
      <returns>Additional status information about the completion of an operation.</returns>
    </member>
    <member name="T:System.Management.CompletedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Management.ManagementOperationObserver.Completed" /> event.</summary>
      <param name="sender">The instance of the object for which to invoke this method.</param>
      <param name="e">The <see cref="T:System.Management.CompletedEventArgs" /> that specifies the reason the event was invoked.</param>
    </member>
    <member name="T:System.Management.ConnectionOptions">
      <summary>Specifies all settings required to make a WMI connection.</summary>
    </member>
    <member name="M:System.Management.ConnectionOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ConnectionOptions" /> class for the connection operation, using default values. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ConnectionOptions.#ctor(System.String,System.String,System.Security.SecureString,System.String,System.Management.ImpersonationLevel,System.Management.AuthenticationLevel,System.Boolean,System.Management.ManagementNamedValueCollection,System.TimeSpan)">
      <summary>Creates a new ConnectionOption.</summary>
      <param name="locale">The locale to be used for the connection.</param>
      <param name="username">The user name to be used for the connection. If null, the credentials of the currently logged-on user are used.</param>
      <param name="password">The password for the given user name. If the user name is also null, the credentials used will be those of the currently logged-on user.</param>
      <param name="authority">The authority to be used to authenticate the specified user.</param>
      <param name="impersonation">The COM impersonation level to be used for the connection.</param>
      <param name="authentication">The COM authentication level to be used for the connection.</param>
      <param name="enablePrivileges">true to enable special user privileges; otherwise, false. This parameter should only be used when performing an operation that requires special Windows NT user privileges.</param>
      <param name="context">A provider-specific, named value pairs object to be passed through to the provider.</param>
      <param name="timeout">Reserved for future use.</param>
    </member>
    <member name="M:System.Management.ConnectionOptions.#ctor(System.String,System.String,System.String,System.String,System.Management.ImpersonationLevel,System.Management.AuthenticationLevel,System.Boolean,System.Management.ManagementNamedValueCollection,System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ConnectionOptions" /> class to be used for a WMI connection, using the specified values.</summary>
      <param name="locale">The locale to be used for the connection.</param>
      <param name="username">The user name to be used for the connection. If null, the credentials of the currently logged-on user are used.</param>
      <param name="password">The password for the given user name. If the user name is also null, the credentials used will be those of the currently logged-on user.</param>
      <param name="authority">The authority to be used to authenticate the specified user.</param>
      <param name="impersonation">The COM impersonation level to be used for the connection.</param>
      <param name="authentication">The COM authentication level to be used for the connection.</param>
      <param name="enablePrivileges">
        <see langword="true" /> to enable special user privileges; otherwise, <see langword="false" />. This parameter should only be used when performing an operation that requires special Windows NT user privileges.</param>
      <param name="context">A provider-specific, named value pairs object to be passed through to the provider.</param>
      <param name="timeout">Reserved for future use.</param>
    </member>
    <member name="P:System.Management.ConnectionOptions.Authentication">
      <summary>Gets or sets the COM authentication level to be used for operations in this connection.</summary>
      <returns>Returns an <see cref="T:System.Management.AuthenticationLevel" /> enumeration value indicating the COM authentication level used for a connection to the local or a remote computer.</returns>
    </member>
    <member name="P:System.Management.ConnectionOptions.Authority">
      <summary>Gets or sets the authority to be used to authenticate the specified user.</summary>
      <returns>Returns a <see cref="T:System.String" /> that defines the authority used to authenticate the specified user.</returns>
    </member>
    <member name="M:System.Management.ConnectionOptions.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="P:System.Management.ConnectionOptions.EnablePrivileges">
      <summary>Gets or sets a value indicating whether user privileges need to be enabled for the connection operation. This property should only be used when the operation performed requires a certain user privilege to be enabled (for example, a machine restart).</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether user privileges need to be enabled for the connection operation.</returns>
    </member>
    <member name="P:System.Management.ConnectionOptions.Impersonation">
      <summary>Gets or sets the COM impersonation level to be used for operations in this connection.</summary>
      <returns>Returns an <see cref="T:System.Management.ImpersonationLevel" /> enumeration value indicating the impersonation level used to connect to WMI.</returns>
    </member>
    <member name="P:System.Management.ConnectionOptions.Locale">
      <summary>Gets or sets the locale to be used for the connection operation.</summary>
      <returns>Returns a <see cref="T:System.String" /> value used for the locale in a connection to WMI.</returns>
    </member>
    <member name="P:System.Management.ConnectionOptions.Password">
      <summary>Sets the password for the specified user.</summary>
      <returns>Returns a <see cref="T:System.String" /> value used for the password in a connection to WMI.</returns>
    </member>
    <member name="P:System.Management.ConnectionOptions.SecurePassword">
      <summary>Sets the password for the specified user.</summary>
      <returns>Returns a SecureString value used for the password in a connection to WMI.</returns>
    </member>
    <member name="P:System.Management.ConnectionOptions.Username">
      <summary>Gets or sets the user name to be used for the connection operation.</summary>
      <returns>Returns a <see cref="T:System.String" /> value used as the user name in a connection to WMI.</returns>
    </member>
    <member name="T:System.Management.DeleteOptions">
      <summary>Specifies options for deleting a management object.</summary>
    </member>
    <member name="M:System.Management.DeleteOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.DeleteOptions" /> class for the delete operation, using default values. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.DeleteOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.DeleteOptions" /> class for a delete operation, using the specified values.</summary>
      <param name="context">A provider-specific, named-value pairs object to be passed through to the provider.</param>
      <param name="timeout">The length of time to let the operation perform before it times out. The default value is <see cref="F:System.TimeSpan.MaxValue" />. Setting this parameter will invoke the operation semisynchronously.</param>
    </member>
    <member name="M:System.Management.DeleteOptions.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>A cloned object.</returns>
    </member>
    <member name="T:System.Management.EnumerationOptions">
      <summary>Provides a base class for query and enumeration-related options objects.</summary>
    </member>
    <member name="M:System.Management.EnumerationOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.EnumerationOptions" /> class with default values (see the individual property descriptions for what the default values are). This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.EnumerationOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan,System.Int32,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.EnumerationOptions" /> class to be used for queries or enumerations, allowing the user to specify values for the different options.</summary>
      <param name="context">The options context object containing provider-specific information that can be passed through to the provider.</param>
      <param name="timeout">The time-out value for enumerating through the results.</param>
      <param name="blockSize">The number of items to retrieve at one time from WMI.</param>
      <param name="rewindable">
        <see langword="true" /> to show that the result set is rewindable (allows multiple traversal); otherwise, <see langword="false" />.</param>
      <param name="returnImmediatley">
        <see langword="true" /> to show that the operation should return immediately (semi-sync) or block until all results are available; otherwise, <see langword="false" />.</param>
      <param name="useAmendedQualifiers">
        <see langword="true" /> to show that the returned objects should contain amended (locale-aware) qualifiers; otherwise, <see langword="false" />.</param>
      <param name="ensureLocatable">
        <see langword="true" /> to ensure all returned objects have valid paths; otherwise, <see langword="false" />.</param>
      <param name="prototypeOnly">
        <see langword="true" /> to return a prototype of the result set instead of the actual results; otherwise, <see langword="false" />.</param>
      <param name="directRead">
        <see langword="true" /> to retrieve objects of only the specified class or from derived classes as well; otherwise, <see langword="false" />.</param>
      <param name="enumerateDeep">
        <see langword="true" /> to use recursive enumeration in subclasses; otherwise, <see langword="false" />.</param>
    </member>
    <member name="P:System.Management.EnumerationOptions.BlockSize">
      <summary>Gets or sets the block size for block operations. When enumerating through a collection, WMI will return results in groups of the specified size.</summary>
      <returns>The block size in block operations.</returns>
    </member>
    <member name="M:System.Management.EnumerationOptions.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="P:System.Management.EnumerationOptions.DirectRead">
      <summary>Gets or sets a value indicating whether direct access to the WMI provider is requested for the specified class, without any regard to its super class or derived classes.</summary>
      <returns>
        <see langword="true" /> if direct access to the WMI provider is requested for the specified class; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Management.EnumerationOptions.EnsureLocatable">
      <summary>Gets or sets a value indicating whether to the objects returned should have locatable information in them. This ensures that the system properties, such as __PATH, __RELPATH, and __SERVER, are non-NULL. This flag can only be used in queries, and is ignored in enumerations.</summary>
      <returns>
        <see langword="true" /> if the objects returned should have locatable information in them; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Management.EnumerationOptions.EnumerateDeep">
      <summary>Gets or sets a value indicating whether recursive enumeration is requested into all classes derived from the specified superclass. If <see langword="false" />, only immediate derived class members are returned.</summary>
      <returns>
        <see langword="true" /> if recursive enumeration is requested into all classes derived from the specified superclass; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Management.EnumerationOptions.PrototypeOnly">
      <summary>Gets or sets a value indicating whether the query should return a prototype of the result set instead of the actual results. This flag is used for prototyping.</summary>
      <returns>
        <see langword="true" /> if the query should return a prototype of the result set instead of the actual results; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Management.EnumerationOptions.ReturnImmediately">
      <summary>Gets or sets a value indicating whether the invoked operation should be performed in a synchronous or semisynchronous fashion. If this property is set to <see langword="true" />, the enumeration is invoked and the call returns immediately. The actual retrieval of the results will occur when the resulting collection is walked.</summary>
      <returns>
        <see langword="true" /> if the invoked operation should be performed in a synchronous or semisynchronous fashion; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Management.EnumerationOptions.Rewindable">
      <summary>Gets or sets a value indicating whether the collection is assumed to be rewindable. If <see langword="true" />, the objects in the collection will be kept available for multiple enumerations. If <see langword="false" />, the collection can only be enumerated one time.</summary>
      <returns>
        <see langword="true" /> if the collection is assumed to be rewindable; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Management.EnumerationOptions.UseAmendedQualifiers">
      <summary>Gets or sets a value indicating whether the objects returned from WMI should contain amended information. Typically, amended information is localizable information attached to the WMI object, such as object and property descriptions.</summary>
      <returns>
        <see langword="true" /> if the objects returned from WMI should contain amended information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Management.EventArrivedEventArgs">
      <summary>Holds event data for the <see cref="E:System.Management.ManagementEventWatcher.EventArrived" /> event.</summary>
    </member>
    <member name="P:System.Management.EventArrivedEventArgs.NewEvent">
      <summary>Gets the WMI event that was delivered.</summary>
      <returns>The delivered WMI event.</returns>
    </member>
    <member name="T:System.Management.EventArrivedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Management.ManagementEventWatcher.EventArrived" /> event.</summary>
      <param name="sender">The instance of the object for which to invoke this method.</param>
      <param name="e">The <see cref="T:System.Management.EventArrivedEventArgs" /> that specifies the reason the event was invoked.</param>
    </member>
    <member name="T:System.Management.EventQuery">
      <summary>Represents a WMI event query.</summary>
    </member>
    <member name="M:System.Management.EventQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.EventQuery" /> class. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.EventQuery.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.EventQuery" /> class for the specified query.</summary>
      <param name="query">A textual representation of the <c>event query</c>.</param>
    </member>
    <member name="M:System.Management.EventQuery.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.EventQuery" /> class for the specified language and query.</summary>
      <param name="language">The language in which the query string is specified.</param>
      <param name="query">The string representation of the query.</param>
    </member>
    <member name="M:System.Management.EventQuery.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="T:System.Management.EventWatcherOptions">
      <summary>Specifies options for management event watching.</summary>
    </member>
    <member name="M:System.Management.EventWatcherOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.EventWatcherOptions" /> class for event watching, using default values. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.EventWatcherOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.EventWatcherOptions" /> class with the given values.</summary>
      <param name="context">The options context object containing provider-specific information to be passed through to the provider.</param>
      <param name="timeout">The time-out to wait for the next events.</param>
      <param name="blockSize">The number of events to wait for in each block.</param>
    </member>
    <member name="P:System.Management.EventWatcherOptions.BlockSize">
      <summary>Gets or sets the block size for block operations. When waiting for events, this value specifies how many events to wait for before returning.</summary>
      <returns>An integer value indicating the block size for a block of operations.</returns>
    </member>
    <member name="M:System.Management.EventWatcherOptions.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="T:System.Management.ImpersonationLevel">
      <summary>Describes the impersonation level to be used to connect to WMI.</summary>
    </member>
    <member name="F:System.Management.ImpersonationLevel.Anonymous">
      <summary>Anonymous COM impersonation level that hides the identity of the caller. Calls to WMI may fail with this impersonation level.</summary>
    </member>
    <member name="F:System.Management.ImpersonationLevel.Default">
      <summary>Default impersonation.</summary>
    </member>
    <member name="F:System.Management.ImpersonationLevel.Delegate">
      <summary>Delegate-level COM impersonation level that allows objects to permit other objects to use the credentials of the caller. This level, which will work with WMI calls but may constitute an unnecessary security risk, is supported only under Windows 2000.</summary>
    </member>
    <member name="F:System.Management.ImpersonationLevel.Identify">
      <summary>Identify-level COM impersonation level that allows objects to query the credentials of the caller. Calls to WMI may fail with this impersonation level.</summary>
    </member>
    <member name="F:System.Management.ImpersonationLevel.Impersonate">
      <summary>Impersonate-level COM impersonation level that allows objects to use the credentials of the caller. This is the recommended impersonation level for WMI calls.</summary>
    </member>
    <member name="T:System.Management.InvokeMethodOptions">
      <summary>Specifies options for invoking a management method.</summary>
    </member>
    <member name="M:System.Management.InvokeMethodOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.InvokeMethodOptions" /> class for the <see cref="M:System.Management.ManagementObject.InvokeMethod(System.String,System.Object[])" /> operation, using default values. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.InvokeMethodOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.InvokeMethodOptions" /> class for an invoke operation using the specified values.</summary>
      <param name="context">A provider-specific, named-value pairs object to be passed through to the provider.</param>
      <param name="timeout">The length of time to let the operation perform before it times out. The default value is <see cref="F:System.TimeSpan.MaxValue" />. Setting this parameter will invoke the operation semisynchronously.</param>
    </member>
    <member name="M:System.Management.InvokeMethodOptions.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="T:System.Management.ManagementBaseObject">
      <summary>Contains the basic elements of a management object. It serves as a base class to more specific management object classes.</summary>
    </member>
    <member name="M:System.Management.ManagementBaseObject.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementBaseObject" /> class that is serializable.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext" /> ) for this serialization.</param>
    </member>
    <member name="P:System.Management.ManagementBaseObject.ClassPath">
      <summary>Gets the path to the management object's class.</summary>
      <returns>The class path to the management object's class.</returns>
    </member>
    <member name="M:System.Management.ManagementBaseObject.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The new cloned object.</returns>
    </member>
    <member name="M:System.Management.ManagementBaseObject.CompareTo(System.Management.ManagementBaseObject,System.Management.ComparisonSettings)">
      <summary>Compares this object to another, based on specified options.</summary>
      <param name="otherObject">The object to which to compare this object.</param>
      <param name="settings">Options on how to compare the objects.</param>
      <returns>
        <see langword="true" /> if the objects compared are equal according to the given options; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Management.ManagementBaseObject.Dispose">
      <summary>Releases the unmanaged resources used by the ManagementBaseObject.</summary>
    </member>
    <member name="M:System.Management.ManagementBaseObject.Equals(System.Object)">
      <summary>Compares two management objects.</summary>
      <param name="obj">An object to compare with this instance.</param>
      <returns>
        <see langword="true" /> if this is an instance of <see cref="T:System.Management.ManagementBaseObject" /> and represents the same object as this instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Management.ManagementBaseObject.GetHashCode">
      <summary>Serves as a hash function for a particular type, suitable for use in hashing algorithms and data structures like a hash table.</summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="M:System.Management.ManagementBaseObject.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with the data necessary to deserialize the field represented by this instance.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext" /> ) for this serialization.</param>
    </member>
    <member name="M:System.Management.ManagementBaseObject.GetPropertyQualifierValue(System.String,System.String)">
      <summary>Returns the value of the specified property qualifier.</summary>
      <param name="propertyName">The name of the property to which the qualifier belongs.</param>
      <param name="qualifierName">The name of the property qualifier of interest.</param>
      <returns>The value of the specified qualifier.</returns>
    </member>
    <member name="M:System.Management.ManagementBaseObject.GetPropertyValue(System.String)">
      <summary>Gets an equivalent accessor to a property's value.</summary>
      <param name="propertyName">The name of the property of interest.</param>
      <returns>The value of the specified property.</returns>
    </member>
    <member name="M:System.Management.ManagementBaseObject.GetQualifierValue(System.String)">
      <summary>Gets the value of the specified qualifier.</summary>
      <param name="qualifierName">The name of the qualifier of interest.</param>
      <returns>The value of the specified qualifier.</returns>
    </member>
    <member name="M:System.Management.ManagementBaseObject.GetText(System.Management.TextFormat)">
      <summary>Returns a textual representation of the object in the specified format.</summary>
      <param name="format">The requested textual format.</param>
      <returns>The textual representation of the object in the specified format.</returns>
    </member>
    <member name="P:System.Management.ManagementBaseObject.Item(System.String)">
      <summary>Gets access to property values through [] notation. This property is the indexer for the <see cref="T:System.Management.ManagementBaseObject" /> class. You can use the default indexed properties defined by a type, but you cannot explicitly define your own. However, specifying the expando attribute on a class automatically provides a default indexed property whose type is Object and whose index type is String.</summary>
      <param name="propertyName">The name of the property of interest.</param>
      <returns>The management object for a specific class property.</returns>
    </member>
    <member name="M:System.Management.ManagementBaseObject.op_Explicit(System.Management.ManagementBaseObject)~System.IntPtr">
      <summary>Provides the internal WMI object represented by a <see cref="T:System.Management.ManagementObject" />.</summary>
      <param name="managementObject">The <see cref="T:System.Management.ManagementBaseObject" /> that references the requested WMI object.</param>
      <returns>An <see cref="T:System.IntPtr" /> representing the internal WMI object.</returns>
    </member>
    <member name="P:System.Management.ManagementBaseObject.Properties">
      <summary>Gets a collection of <see cref="T:System.Management.PropertyData" /> objects describing the properties of the management object.</summary>
      <returns>A collection that holds the properties for the management object.</returns>
    </member>
    <member name="P:System.Management.ManagementBaseObject.Qualifiers">
      <summary>Gets the collection of <see cref="T:System.Management.QualifierData" /> objects defined on the management object. Each element in the collection holds information such as the qualifier name, value, and flavor.</summary>
      <returns>A collection that holds the qualifiers for the management object.</returns>
    </member>
    <member name="M:System.Management.ManagementBaseObject.SetPropertyQualifierValue(System.String,System.String,System.Object)">
      <summary>Sets the value of the specified property qualifier.</summary>
      <param name="propertyName">The name of the property to which the qualifier belongs.</param>
      <param name="qualifierName">The name of the property qualifier of interest.</param>
      <param name="qualifierValue">The new value for the qualifier.</param>
    </member>
    <member name="M:System.Management.ManagementBaseObject.SetPropertyValue(System.String,System.Object)">
      <summary>Sets the value of the named property.</summary>
      <param name="propertyName">The name of the property to be changed.</param>
      <param name="propertyValue">The new value for this property.</param>
    </member>
    <member name="M:System.Management.ManagementBaseObject.SetQualifierValue(System.String,System.Object)">
      <summary>Sets the value of the named qualifier.</summary>
      <param name="qualifierName">The name of the qualifier to set. This parameter cannot be null.</param>
      <param name="qualifierValue">The value to set.</param>
    </member>
    <member name="M:System.Management.ManagementBaseObject.System#Runtime#Serialization#ISerializable#GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Implements the <see cref="T:System.Runtime.Serialization.ISerializable" /> interface and returns the data needed to serialize the <see cref="T:System.Management.ManagementBaseObject" />.</summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> containing the information required to serialize the <see cref="T:System.Management.ManagementBaseObject" />.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> containing the source and destination of the serialized stream associated with the <see cref="T:System.Management.ManagementBaseObject" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="info" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Management.ManagementBaseObject.SystemProperties">
      <summary>Gets  the collection of WMI system properties of the management object (for example, the class name, server, and namespace). WMI system property names begin with "__".</summary>
      <returns>A collection that contains the system properties for a management object.</returns>
    </member>
    <member name="T:System.Management.ManagementClass">
      <summary>Represents a Common Information Model (CIM) management class. A management class is a WMI class such as Win32_LogicalDisk, which can represent a disk drive, and Win32_Process, which represents a process such as Notepad.exe. The members of this class enable you to access WMI data using a specific WMI class path. For more information, see "Win32 Classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</summary>
    </member>
    <member name="M:System.Management.ManagementClass.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementClass" /> class. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ManagementClass.#ctor(System.Management.ManagementPath)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementClass" /> class. The class represents a Common Information Model (CIM) management class from WMI such as Win32_LogicalDisk, which can represent a disk drive, and Win32_Process, which represents a process such as Notepad.exe. For more information, see "Win32 Classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</summary>
      <param name="path">A <see cref="T:System.Management.ManagementPath" /> specifying the WMI class to which to bind. The parameter must specify a WMI class path. The class represents a CIM management class from WMI. CIM classes represent management information including hardware, software, processes, and so on. For more information about the CIM classes available in Windows, see "Win32 classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</param>
    </member>
    <member name="M:System.Management.ManagementClass.#ctor(System.Management.ManagementPath,System.Management.ObjectGetOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementClass" /> class initialized to the given WMI class path using the specified options. The class represents a Common Information Model (CIM) management class from WMI such as Win32_LogicalDisk, which can represent a disk drive, and Win32_Process, which represents a process such as Notepad.exe. For more information, see "Win32 Classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</summary>
      <param name="path">A <see cref="T:System.Management.ManagementPath" /> instance representing the WMI class path. The class represents a CIM management class from WMI. CIM classes represent management information including hardware, software, processes, and so on. For more information about the CIM classes available in Windows, see "Win32 classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</param>
      <param name="options">An <see cref="T:System.Management.ObjectGetOptions" /> representing the options to use when retrieving this class.</param>
    </member>
    <member name="M:System.Management.ManagementClass.#ctor(System.Management.ManagementScope,System.Management.ManagementPath,System.Management.ObjectGetOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementClass" /> class for the specified WMI class in the specified scope and with the specified options. The class represents a Common Information Model (CIM) management class from WMI such as Win32_LogicalDisk, which can represent a disk drive, and Win32_Process, which represents a process such as Notepad.exe. For more information, see "Win32 Classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</summary>
      <param name="scope">A <see cref="T:System.Management.ManagementScope" /> that specifies the scope (server and namespace) where the WMI class resides.</param>
      <param name="path">A <see cref="T:System.Management.ManagementPath" /> that represents the path to the WMI class in the specified scope. The class represents a CIM management class from WMI. CIM classes represent management information including hardware, software, processes, and so on. For more information about the CIM classes available in Windows, see "Win32 classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</param>
      <param name="options">An <see cref="T:System.Management.ObjectGetOptions" /> that specifies the options to use when retrieving the WMI class.</param>
    </member>
    <member name="M:System.Management.ManagementClass.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementClass" /> class from the specified instances of the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" /> classes.</summary>
      <param name="info">An instance of the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> class containing the information required to serialize the new <see cref="T:System.Management.ManagementClass" />.</param>
      <param name="context">An instance of the <see cref="T:System.Runtime.Serialization.StreamingContext" /> class containing the source of the serialized stream associated with the new <see cref="T:System.Management.ManagementClass" />.</param>
    </member>
    <member name="M:System.Management.ManagementClass.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementClass" /> class initialized to the given path. The class represents a Common Information Model (CIM) management class from WMI such as Win32_LogicalDisk, which can represent a disk drive, and Win32_Process, which represents a process such as Notepad.exe. For more information, see "Win32 Classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</summary>
      <param name="path">The path to the WMI class. The class represents a CIM management class from WMI. CIM classes represent management information including hardware, software, processes, and so on. For more information about the CIM classes available in Windows, see "Win32 classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</param>
    </member>
    <member name="M:System.Management.ManagementClass.#ctor(System.String,System.Management.ObjectGetOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementClass" /> class initialized to the given WMI class path using the specified options. The class represents a Common Information Model (CIM) management class from WMI such as Win32_LogicalDisk, which can represent a disk drive, and Win32_Process, which represents a process such as Notepad.exe. For more information, see "Win32 Classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</summary>
      <param name="path">The path to the WMI class. The class represents a CIM management class from WMI. CIM classes represent management information including hardware, software, processes, and so on. For more information about the CIM classes available in Windows, see "Win32 classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</param>
      <param name="options">An <see cref="T:System.Management.ObjectGetOptions" /> representing the options to use when retrieving the WMI class.</param>
    </member>
    <member name="M:System.Management.ManagementClass.#ctor(System.String,System.String,System.Management.ObjectGetOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementClass" /> class for the specified WMI class, in the specified scope, and with the specified options. The class represents a Common Information Model (CIM) management class from WMI such as Win32_LogicalDisk, which can represent a disk drive, and Win32_Process, which represents a process such as Notepad.exe. For more information, see "Win32 Classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</summary>
      <param name="scope">The scope in which the WMI class resides.</param>
      <param name="path">The path to the WMI class within the specified scope. The class represents a CIM management class from WMI. CIM classes represent management information including hardware, software, processes, and so on. For more information about the CIM classes available in Windows, see "Win32 classes" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</param>
      <param name="options">An <see cref="T:System.Management.ObjectGetOptions" /> that specifies the options to use when retrieving the WMI class.</param>
    </member>
    <member name="M:System.Management.ManagementClass.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.CreateInstance">
      <summary>Initializes a new instance of the WMI class.</summary>
      <returns>A <see cref="T:System.Management.ManagementObject" /> that represents a new instance of the WMI class.</returns>
    </member>
    <member name="P:System.Management.ManagementClass.Derivation">
      <summary>Gets an array containing all WMI classes in the inheritance hierarchy from this class to the top of the hierarchy.</summary>
      <returns>A string collection containing the names of all WMI classes in the inheritance hierarchy of this class.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.Derive(System.String)">
      <summary>Derives a new class from this class.</summary>
      <param name="newClassName">The name of the new class to be derived.</param>
      <returns>A new <see cref="T:System.Management.ManagementClass" /> that represents a new WMI class derived from the original class.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetInstances">
      <summary>Returns the collection of all instances of the class.</summary>
      <returns>A collection of the <see cref="T:System.Management.ManagementObject" /> objects representing the instances of the class.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetInstances(System.Management.EnumerationOptions)">
      <summary>Returns the collection of all instances of the class using the specified options.</summary>
      <param name="options">The additional operation options.</param>
      <returns>A collection of the <see cref="T:System.Management.ManagementObject" /> objects representing the instances of the class, according to the specified options.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetInstances(System.Management.ManagementOperationObserver)">
      <summary>Returns the collection of all instances of the class, asynchronously.</summary>
      <param name="watcher">The object to handle the asynchronous operation's progress.</param>
    </member>
    <member name="M:System.Management.ManagementClass.GetInstances(System.Management.ManagementOperationObserver,System.Management.EnumerationOptions)">
      <summary>Returns the collection of all instances of the class, asynchronously, using the specified options.</summary>
      <param name="watcher">The object to handle the asynchronous operation's progress.</param>
      <param name="options">The specified additional options for getting the instances.</param>
    </member>
    <member name="M:System.Management.ManagementClass.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with the data necessary to deserialize the field represented by this instance.</summary>
      <param name="info">The object to be populated with serialization information.</param>
      <param name="context">The location where serialized data will be stored and retrieved.</param>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelatedClasses">
      <summary>Retrieves classes related to the WMI class.</summary>
      <returns>A collection of the <see cref="T:System.Management.ManagementClass" /> or <see cref="T:System.Management.ManagementObject" /> objects that represents WMI classes or instances related to the WMI class.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelatedClasses(System.Management.ManagementOperationObserver)">
      <summary>Retrieves classes related to the WMI class, asynchronously.</summary>
      <param name="watcher">The object to handle the asynchronous operation's progress.</param>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelatedClasses(System.Management.ManagementOperationObserver,System.String)">
      <summary>Retrieves classes related to the WMI class, asynchronously, given the related class name.</summary>
      <param name="watcher">The object to handle the asynchronous operation's progress.</param>
      <param name="relatedClass">The name of the related class.</param>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelatedClasses(System.Management.ManagementOperationObserver,System.String,System.String,System.String,System.String,System.String,System.String,System.Management.EnumerationOptions)">
      <summary>Retrieves classes related to the WMI class, asynchronously, using the specified options.</summary>
      <param name="watcher">Handler for progress and results of the asynchronous operation.</param>
      <param name="relatedClass">The class from which resulting classes have to be derived.</param>
      <param name="relationshipClass">The relationship type which resulting classes must have with the source class.</param>
      <param name="relationshipQualifier">This qualifier must be present on the relationship.</param>
      <param name="relatedQualifier">This qualifier must be present on the resulting classes.</param>
      <param name="relatedRole">The resulting classes must have this role in the relationship.</param>
      <param name="thisRole">The source class must have this role in the relationship.</param>
      <param name="options">The options for retrieving the resulting classes.</param>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelatedClasses(System.String)">
      <summary>Retrieves classes related to the WMI class.</summary>
      <param name="relatedClass">The class from which resulting classes have to be derived.</param>
      <returns>A collection of classes related to this class.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelatedClasses(System.String,System.String,System.String,System.String,System.String,System.String,System.Management.EnumerationOptions)">
      <summary>Retrieves classes related to the WMI class based on the specified options.</summary>
      <param name="relatedClass">The class from which resulting classes have to be derived.</param>
      <param name="relationshipClass">The relationship type which resulting classes must have with the source class.</param>
      <param name="relationshipQualifier">This qualifier must be present on the relationship.</param>
      <param name="relatedQualifier">This qualifier must be present on the resulting classes.</param>
      <param name="relatedRole">The resulting classes must have this role in the relationship.</param>
      <param name="thisRole">The source class must have this role in the relationship.</param>
      <param name="options">The options for retrieving the resulting classes.</param>
      <returns>A collection of classes related to this class.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelationshipClasses">
      <summary>Retrieves relationship classes that relate the class to others.</summary>
      <returns>A collection of association classes that relate the class to any other class.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelationshipClasses(System.Management.ManagementOperationObserver)">
      <summary>Retrieves relationship classes that relate the class to others, asynchronously.</summary>
      <param name="watcher">The object to handle the asynchronous operation's progress.</param>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelationshipClasses(System.Management.ManagementOperationObserver,System.String)">
      <summary>Retrieves relationship classes that relate the class to the specified WMI class, asynchronously.</summary>
      <param name="watcher">The object to handle the asynchronous operation's progress.</param>
      <param name="relationshipClass">The WMI class to which all returned relationships should point.</param>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelationshipClasses(System.Management.ManagementOperationObserver,System.String,System.String,System.String,System.Management.EnumerationOptions)">
      <summary>Retrieves relationship classes that relate the class according to the specified options, asynchronously.</summary>
      <param name="watcher">The handler for progress and results of the asynchronous operation.</param>
      <param name="relationshipClass">The class from which all resulting relationship classes must derive.</param>
      <param name="relationshipQualifier">The qualifier which the resulting relationship classes must have.</param>
      <param name="thisRole">The role which the source class must have in the resulting relationship classes.</param>
      <param name="options">The options for retrieving the results.</param>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelationshipClasses(System.String)">
      <summary>Retrieves relationship classes that relate the class to others, where the endpoint class is the specified class.</summary>
      <param name="relationshipClass">The endpoint class for all relationship classes returned.</param>
      <returns>A collection of association classes that relate the class to the specified class. For more information about relationship classes, see "ASSOCIATORS OF Statement" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetRelationshipClasses(System.String,System.String,System.String,System.Management.EnumerationOptions)">
      <summary>Retrieves relationship classes that relate this class to others, according to specified options.</summary>
      <param name="relationshipClass">All resulting relationship classes must derive from this class.</param>
      <param name="relationshipQualifier">Resulting relationship classes must have this qualifier.</param>
      <param name="thisRole">The source class must have this role in the resulting relationship classes.</param>
      <param name="options">Specifies options for retrieving the results.</param>
      <returns>A collection of association classes that relate this class to others, according to the specified options. For more information about relationship classes, see "ASSOCIATORS OF Statement" in the Windows Management Instrumentation documentation in the MSDN Library at http://msdn.microsoft.com/library.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetStronglyTypedClassCode(System.Boolean,System.Boolean)">
      <summary>Generates a strongly-typed class for a given WMI class.</summary>
      <param name="includeSystemClassInClassDef">
        <see langword="true" /> to include the class for managing system properties; otherwise, <see langword="false" />.</param>
      <param name="systemPropertyClass">
        <see langword="true" /> to have the generated class manage system properties; otherwise, <see langword="false" />.</param>
      <returns>A <see cref="T:System.CodeDom.CodeTypeDeclaration" /> representing the declaration for the strongly-typed class.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetStronglyTypedClassCode(System.Management.CodeLanguage,System.String,System.String)">
      <summary>Generates a strongly-typed class for a given WMI class. This function generates code for Visual Basic, C#, JScript, J#, or C++ depending on the input parameters.</summary>
      <param name="lang">The language of the code to be generated. This code language comes from the <see cref="T:System.Management.CodeLanguage" /> enumeration.</param>
      <param name="filePath">The path of the file where the code is to be written.</param>
      <param name="classNamespace">The.NET namespace into which the class should be generated. If this is empty, the namespace will be generated from the WMI namespace.</param>
      <returns>
        <see langword="true" />, if the method succeeded; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetSubclasses">
      <summary>Returns the collection of all subclasses for the class.</summary>
      <returns>A collection of the <see cref="T:System.Management.ManagementObject" /> objects that represent the subclasses of the WMI class.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetSubclasses(System.Management.EnumerationOptions)">
      <summary>Retrieves the subclasses of the class using the specified options.</summary>
      <param name="options">The specified additional options for retrieving subclasses of the class.</param>
      <returns>A collection of the <see cref="T:System.Management.ManagementObject" /> objects representing the subclasses of the WMI class, according to the specified options.</returns>
    </member>
    <member name="M:System.Management.ManagementClass.GetSubclasses(System.Management.ManagementOperationObserver)">
      <summary>Returns the collection of all classes derived from this class, asynchronously.</summary>
      <param name="watcher">The object to handle the asynchronous operation's progress.</param>
    </member>
    <member name="M:System.Management.ManagementClass.GetSubclasses(System.Management.ManagementOperationObserver,System.Management.EnumerationOptions)">
      <summary>Retrieves all classes derived from this class, asynchronously, using the specified options.</summary>
      <param name="watcher">The object to handle the asynchronous operation's progress.</param>
      <param name="options">The specified additional options to use in the derived class retrieval.</param>
    </member>
    <member name="P:System.Management.ManagementClass.Methods">
      <summary>Gets or sets a collection of <see cref="T:System.Management.MethodData" /> objects that represent the methods defined in the WMI class.</summary>
      <returns>A <see cref="T:System.Management.MethodDataCollection" /> representing the methods defined in the WMI class.</returns>
    </member>
    <member name="P:System.Management.ManagementClass.Path">
      <summary>Gets or sets the path of the WMI class to which the <see cref="T:System.Management.ManagementClass" /> object is bound.</summary>
      <returns>The path of the object's class.</returns>
    </member>
    <member name="T:System.Management.ManagementDateTimeConverter">
      <summary>Provides methods to convert DMTF datetime and time intervals to CLR-compliant <see cref="T:System.DateTime" /> and <see cref="T:System.TimeSpan" /> format and vice versa.</summary>
    </member>
    <member name="M:System.Management.ManagementDateTimeConverter.ToDateTime(System.String)">
      <summary>Converts a given DMTF datetime to <see cref="T:System.DateTime" />. The returned <see cref="T:System.DateTime" /> will be in the current time zone of the system.</summary>
      <param name="dmtfDate">A string representing the datetime in DMTF format.</param>
      <returns>A <see cref="T:System.DateTime" /> that represents the given DMTF datetime.</returns>
    </member>
    <member name="M:System.Management.ManagementDateTimeConverter.ToDmtfDateTime(System.DateTime)">
      <summary>Converts a given <see cref="T:System.DateTime" /> to DMTF datetime format.</summary>
      <param name="date">A <see cref="T:System.DateTime" /> representing the datetime to be converted to DMTF datetime.</param>
      <returns>A string that represents the DMTF datetime for the given <see cref="T:System.DateTime" />.</returns>
    </member>
    <member name="M:System.Management.ManagementDateTimeConverter.ToDmtfTimeInterval(System.TimeSpan)">
      <summary>Converts a given <see cref="T:System.TimeSpan" /> to DMTF time interval.</summary>
      <param name="timespan">A <see cref="T:System.TimeSpan" /> representing the datetime to be converted to DMTF time interval.</param>
      <returns>A string that represents the DMTF time interval for the given <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="M:System.Management.ManagementDateTimeConverter.ToTimeSpan(System.String)">
      <summary>Converts a given DMTF time interval to a <see cref="T:System.TimeSpan" />.</summary>
      <param name="dmtfTimespan">A string representation of the DMTF time interval.</param>
      <returns>A <see cref="T:System.TimeSpan" /> that represents the given DMTF time interval.</returns>
    </member>
    <member name="T:System.Management.ManagementEventArgs">
      <summary>Represents the virtual base class to hold event data for WMI events.</summary>
    </member>
    <member name="P:System.Management.ManagementEventArgs.Context">
      <summary>Gets the operation context echoed back from the operation that triggered the event.</summary>
      <returns>The operation context.</returns>
    </member>
    <member name="T:System.Management.ManagementEventWatcher">
      <summary>Subscribes to temporary event notifications based on a specified event query.</summary>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementEventWatcher" /> class. For further initialization, set the properties on the object. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.#ctor(System.Management.EventQuery)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementEventWatcher" /> class when given a WMI event query.</summary>
      <param name="query">An <see cref="T:System.Management.EventQuery" /> representing a WMI event query, which determines the events for which the watcher will listen.</param>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.#ctor(System.Management.ManagementScope,System.Management.EventQuery)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementEventWatcher" /> class that listens for events conforming to the given WMI event query.</summary>
      <param name="scope">A <see cref="T:System.Management.ManagementScope" /> representing the scope (namespace) in which the watcher will listen for events.</param>
      <param name="query">An <see cref="T:System.Management.EventQuery" /> representing a WMI event query, which determines the events for which the watcher will listen.</param>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.#ctor(System.Management.ManagementScope,System.Management.EventQuery,System.Management.EventWatcherOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementEventWatcher" /> class that listens for events conforming to the given WMI event query, according to the specified options. For this variant, the query and the scope are specified objects. The options object can specify options such as time-out and context information.</summary>
      <param name="scope">A <see cref="T:System.Management.ManagementScope" /> representing the scope (namespace) in which the watcher will listen for events.</param>
      <param name="query">An <see cref="T:System.Management.EventQuery" /> representing a WMI event query, which determines the events for which the watcher will listen.</param>
      <param name="options">An <see cref="T:System.Management.EventWatcherOptions" /> representing additional options used to watch for events.</param>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementEventWatcher" /> class when given a WMI event query in the form of a string.</summary>
      <param name="query">A WMI event query, which defines the events for which the watcher will listen.</param>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementEventWatcher" /> class that listens for events conforming to the given WMI event query. For this variant, the query and the scope are specified as strings.</summary>
      <param name="scope">The management scope (namespace) in which the watcher will listen for events.</param>
      <param name="query">The query that defines the events for which the watcher will listen.</param>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.#ctor(System.String,System.String,System.Management.EventWatcherOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementEventWatcher" /> class that listens for events conforming to the given WMI event query, according to the specified options. For this variant, the query and the scope are specified as strings. The options object can specify options such as a time-out and context information.</summary>
      <param name="scope">The management scope (namespace) in which the watcher will listen for events.</param>
      <param name="query">The query that defines the events for which the watcher will listen.</param>
      <param name="options">An <see cref="T:System.Management.EventWatcherOptions" /> representing additional options used to watch for events.</param>
    </member>
    <member name="E:System.Management.ManagementEventWatcher.EventArrived">
      <summary>Occurs when a new event arrives.</summary>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.Finalize">
      <summary>Ensures that outstanding calls are cleared. This is the destructor for the object. In C#, finalizers are expressed using destructor syntax.</summary>
    </member>
    <member name="P:System.Management.ManagementEventWatcher.Options">
      <summary>Gets or sets the options used to watch for events.</summary>
      <returns>The event options used to watch for events.</returns>
    </member>
    <member name="P:System.Management.ManagementEventWatcher.Query">
      <summary>Gets or sets the criteria to apply to events.</summary>
      <returns>The query to apply to events.</returns>
    </member>
    <member name="P:System.Management.ManagementEventWatcher.Scope">
      <summary>Gets or sets the scope in which to watch for events (namespace or scope).</summary>
      <returns>The scope in which to watch for events.</returns>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.Start">
      <summary>Subscribes to events with the given query and delivers them, asynchronously, through the <see cref="E:System.Management.ManagementEventWatcher.EventArrived" /> event.</summary>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.Stop">
      <summary>Cancels the subscription whether it is synchronous or asynchronous.</summary>
    </member>
    <member name="E:System.Management.ManagementEventWatcher.Stopped">
      <summary>Occurs when a subscription is canceled.</summary>
    </member>
    <member name="M:System.Management.ManagementEventWatcher.WaitForNextEvent">
      <summary>Waits for the next event that matches the specified query to arrive, and then returns it.</summary>
      <returns>A <see cref="T:System.Management.ManagementBaseObject" /> representing the newly arrived event.</returns>
    </member>
    <member name="T:System.Management.ManagementException">
      <summary>Represents management exceptions.</summary>
    </member>
    <member name="M:System.Management.ManagementException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementException" /> class.</summary>
    </member>
    <member name="M:System.Management.ManagementException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementException" /> class that is serializable.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> destination for this serialization.</param>
    </member>
    <member name="M:System.Management.ManagementException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementException" /> class with a specified error message.</summary>
      <param name="message">The message that describes the error.</param>
    </member>
    <member name="M:System.Management.ManagementException.#ctor(System.String,System.Exception)">
      <summary>Initializes an empty new instance of the <see cref="T:System.Management.ManagementException" /> class. If the <paramref name="innerException" /> parameter is not <see langword="null" />, the current exception is raised in a catch block that handles the inner exception.</summary>
      <param name="message">The message that describes the error.</param>
      <param name="innerException">The exception that is the cause of the current exception.</param>
    </member>
    <member name="P:System.Management.ManagementException.ErrorCode">
      <summary>Gets the error code reported by WMI, which caused this exception.</summary>
      <returns>One of the enumeration values that indicates the error code.</returns>
    </member>
    <member name="P:System.Management.ManagementException.ErrorInformation">
      <summary>Gets the extended error object provided by WMI.</summary>
      <returns>The extended error information.</returns>
    </member>
    <member name="M:System.Management.ManagementException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with the data needed to serialize the <see cref="T:System.Management.ManagementException" />.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> destination for this serialization.</param>
    </member>
    <member name="T:System.Management.ManagementNamedValueCollection">
      <summary>Represents a collection of named values suitable for use as context information to WMI operations. The names are case-insensitive.</summary>
    </member>
    <member name="M:System.Management.ManagementNamedValueCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementNamedValueCollection" /> class, which is empty. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ManagementNamedValueCollection.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementNamedValueCollection" /> class that is serializable and uses the specified <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext" /> ) for this serialization.</param>
    </member>
    <member name="M:System.Management.ManagementNamedValueCollection.Add(System.String,System.Object)">
      <summary>Adds a single-named value to the collection.</summary>
      <param name="name">The name of the new value.</param>
      <param name="value">The value to be associated with the name.</param>
    </member>
    <member name="M:System.Management.ManagementNamedValueCollection.Clone">
      <summary>Creates a clone of the collection. Individual values are cloned. If a value does not support cloning, then a <see cref="T:System.NotSupportedException" /> is thrown.</summary>
      <returns>The new copy of the collection.</returns>
    </member>
    <member name="P:System.Management.ManagementNamedValueCollection.Item(System.String)">
      <summary>Gets the value associated with the specified name from this collection. In C#, this property is the indexer for the <see cref="T:System.Management.ManagementNamedValueCollection" /> class.</summary>
      <param name="name">The name of the value to be returned.</param>
      <returns>An object that is associated with the specified name from this collection.</returns>
    </member>
    <member name="M:System.Management.ManagementNamedValueCollection.Remove(System.String)">
      <summary>Removes a single-named value from the collection. If the collection does not contain an element with the specified name, the collection remains unchanged and no exception is thrown.</summary>
      <param name="name">The name of the value to be removed.</param>
    </member>
    <member name="M:System.Management.ManagementNamedValueCollection.RemoveAll">
      <summary>Removes all entries from the collection.</summary>
    </member>
    <member name="T:System.Management.ManagementObject">
      <summary>Represents a WMI instance.</summary>
    </member>
    <member name="M:System.Management.ManagementObject.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObject" /> class. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ManagementObject.#ctor(System.Management.ManagementPath)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObject" /> class for the specified WMI object path. The path is provided as a <see cref="T:System.Management.ManagementPath" />.</summary>
      <param name="path">A <see cref="T:System.Management.ManagementPath" /> that contains a path to a WMI object.</param>
    </member>
    <member name="M:System.Management.ManagementObject.#ctor(System.Management.ManagementPath,System.Management.ObjectGetOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObject" /> class bound to the specified WMI path, including the specified additional options.</summary>
      <param name="path">A <see cref="T:System.Management.ManagementPath" /> containing the WMI path.</param>
      <param name="options">An <see cref="T:System.Management.ObjectGetOptions" /> containing additional options for binding to the WMI object. This parameter could be null if default options are to be used.</param>
    </member>
    <member name="M:System.Management.ManagementObject.#ctor(System.Management.ManagementScope,System.Management.ManagementPath,System.Management.ObjectGetOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObject" /> class bound to the specified WMI path that includes the specified options.</summary>
      <param name="scope">A <see cref="T:System.Management.ManagementScope" /> representing the scope in which the WMI object resides. In this version, scopes can only be WMI namespaces.</param>
      <param name="path">A <see cref="T:System.Management.ManagementPath" /> representing the WMI path to the manageable object.</param>
      <param name="options">An <see cref="T:System.Management.ObjectGetOptions" /> specifying additional options for getting the object.</param>
    </member>
    <member name="M:System.Management.ManagementObject.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObject" /> class that is serializable.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> to populate with data.</param>
      <param name="context">The destination (see <see cref="T:System.Runtime.Serialization.StreamingContext" />) for this serialization.</param>
    </member>
    <member name="M:System.Management.ManagementObject.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObject" /> class for the specified WMI object path. The path is provided as a string.</summary>
      <param name="path">A WMI path.</param>
    </member>
    <member name="M:System.Management.ManagementObject.#ctor(System.String,System.Management.ObjectGetOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObject" /> class bound to the specified WMI path, including the specified additional options. In this variant, the path can be specified as a string.</summary>
      <param name="path">The WMI path to the object.</param>
      <param name="options">An <see cref="T:System.Management.ObjectGetOptions" /> representing options to get the specified WMI object.</param>
    </member>
    <member name="M:System.Management.ManagementObject.#ctor(System.String,System.String,System.Management.ObjectGetOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObject" /> class bound to the specified WMI path, and includes the specified options. The scope and the path are specified as strings.</summary>
      <param name="scopeString">The scope for the WMI object.</param>
      <param name="pathString">The WMI object path.</param>
      <param name="options">An <see cref="T:System.Management.ObjectGetOptions" /> representing additional options for getting the WMI object.</param>
    </member>
    <member name="P:System.Management.ManagementObject.ClassPath">
      <summary>Gets or sets the path to the object's class.</summary>
      <returns>A <see cref="T:System.Management.ManagementPath" /> representing the path to the object's class.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.Clone">
      <summary>Creates a copy of the object.</summary>
      <returns>The copied object.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementOperationObserver,System.Management.ManagementPath)">
      <summary>Copies the object to a different location, asynchronously.</summary>
      <param name="watcher">The object that will receive the results of the operation.</param>
      <param name="path">A <see cref="T:System.Management.ManagementPath" /> specifying the path to which the object should be copied.</param>
    </member>
    <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementOperationObserver,System.Management.ManagementPath,System.Management.PutOptions)">
      <summary>Copies the object to a different location, asynchronously.</summary>
      <param name="watcher">The object that will receive the results of the operation.</param>
      <param name="path">The path to which the object should be copied.</param>
      <param name="options">The options for how the object should be put.</param>
    </member>
    <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementOperationObserver,System.String)">
      <summary>Copies the object to a different location, asynchronously.</summary>
      <param name="watcher">The object that will receive the results of the operation.</param>
      <param name="path">The path to which the object should be copied.</param>
    </member>
    <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementOperationObserver,System.String,System.Management.PutOptions)">
      <summary>Copies the object to a different location, asynchronously.</summary>
      <param name="watcher">The object that will receive the results of the operation.</param>
      <param name="path">The path to which the object should be copied.</param>
      <param name="options">The options for how the object should be put.</param>
    </member>
    <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementPath)">
      <summary>Copies the object to a different location.</summary>
      <param name="path">The <see cref="T:System.Management.ManagementPath" /> to which the object should be copied.</param>
      <returns>The new path of the copied object.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.CopyTo(System.Management.ManagementPath,System.Management.PutOptions)">
      <summary>Copies the object to a different location.</summary>
      <param name="path">The <see cref="T:System.Management.ManagementPath" /> to which the object should be copied.</param>
      <param name="options">The options for how the object should be put.</param>
      <returns>The new path of the copied object.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.CopyTo(System.String)">
      <summary>Copies the object to a different location.</summary>
      <param name="path">The path to which the object should be copied.</param>
      <returns>The new path of the copied object.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.CopyTo(System.String,System.Management.PutOptions)">
      <summary>Copies the object to a different location.</summary>
      <param name="path">The path to which the object should be copied.</param>
      <param name="options">The options for how the object should be put.</param>
      <returns>The new path of the copied object.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.Delete">
      <summary>Deletes the object.</summary>
    </member>
    <member name="M:System.Management.ManagementObject.Delete(System.Management.DeleteOptions)">
      <summary>Deletes the object.</summary>
      <param name="options">The options for how to delete the object.</param>
    </member>
    <member name="M:System.Management.ManagementObject.Delete(System.Management.ManagementOperationObserver)">
      <summary>Deletes the object.</summary>
      <param name="watcher">The object that will receive the results of the operation.</param>
    </member>
    <member name="M:System.Management.ManagementObject.Delete(System.Management.ManagementOperationObserver,System.Management.DeleteOptions)">
      <summary>Deletes the object.</summary>
      <param name="watcher">The object that will receive the results of the operation.</param>
      <param name="options">The options for how to delete the object.</param>
    </member>
    <member name="M:System.Management.ManagementObject.Dispose">
      <summary>Releases all resources used by the Component.</summary>
    </member>
    <member name="M:System.Management.ManagementObject.Get">
      <summary>Binds WMI class information to the management object.</summary>
    </member>
    <member name="M:System.Management.ManagementObject.Get(System.Management.ManagementOperationObserver)">
      <summary>Binds to the management object asynchronously.</summary>
      <param name="watcher">The object to receive the results of the operation as events.</param>
    </member>
    <member name="M:System.Management.ManagementObject.GetMethodParameters(System.String)">
      <summary>Returns a <see cref="T:System.Management.ManagementBaseObject" /> representing the list of input parameters for a method.</summary>
      <param name="methodName">The name of the method.</param>
      <returns>A <see cref="T:System.Management.ManagementBaseObject" /> containing the input parameters to the method.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Populates a <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with the data necessary to deserialize the field represented by this instance.</summary>
      <param name="info">The object to be populated with serialization information.</param>
      <param name="context">The location where serialized data will be stored and retrieved.</param>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelated">
      <summary>Gets a collection of objects related to the object (associators).</summary>
      <returns>A <see cref="T:System.Management.ManagementObjectCollection" /> containing the related objects.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelated(System.Management.ManagementOperationObserver)">
      <summary>Gets a collection of objects related to the object (associators) asynchronously. This call returns immediately, and a delegate is called when the results are available.</summary>
      <param name="watcher">The object to use to return results.</param>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelated(System.Management.ManagementOperationObserver,System.String)">
      <summary>Gets a collection of objects related to the object (associators).</summary>
      <param name="watcher">The object to use to return results.</param>
      <param name="relatedClass">The class of related objects.</param>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelated(System.Management.ManagementOperationObserver,System.String,System.String,System.String,System.String,System.String,System.String,System.Boolean,System.Management.EnumerationOptions)">
      <summary>Gets a collection of objects related to the object (associators).</summary>
      <param name="watcher">The object to use to return results.</param>
      <param name="relatedClass">The class of the related objects.</param>
      <param name="relationshipClass">The relationship class of interest.</param>
      <param name="relationshipQualifier">The qualifier required to be present on the relationship class.</param>
      <param name="relatedQualifier">The qualifier required to be present on the related class.</param>
      <param name="relatedRole">The role that the related class is playing in the relationship.</param>
      <param name="thisRole">The role that this class is playing in the relationship.</param>
      <param name="classDefinitionsOnly">Return only class definitions for the instances that match the query.</param>
      <param name="options">Extended options for how to execute the query.</param>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelated(System.String)">
      <summary>Gets a collection of objects related to the object (associators).</summary>
      <param name="relatedClass">A class of related objects.</param>
      <returns>A <see cref="T:System.Management.ManagementObjectCollection" /> containing the related objects.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelated(System.String,System.String,System.String,System.String,System.String,System.String,System.Boolean,System.Management.EnumerationOptions)">
      <summary>Gets a collection of objects related to the object (associators).</summary>
      <param name="relatedClass">The class of the related objects.</param>
      <param name="relationshipClass">The relationship class of interest.</param>
      <param name="relationshipQualifier">The qualifier required to be present on the relationship class.</param>
      <param name="relatedQualifier">The qualifier required to be present on the related class.</param>
      <param name="relatedRole">The role that the related class is playing in the relationship.</param>
      <param name="thisRole">The role that this class is playing in the relationship.</param>
      <param name="classDefinitionsOnly">When this method returns, it contains only class definitions for the instances that match the query.</param>
      <param name="options">Extended options for how to execute the query.</param>
      <returns>A <see cref="T:System.Management.ManagementObjectCollection" /> containing the related objects.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelationships">
      <summary>Gets a collection of associations to the object.</summary>
      <returns>A <see cref="T:System.Management.ManagementObjectCollection" /> containing the association objects.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelationships(System.Management.ManagementOperationObserver)">
      <summary>Gets a collection of associations to the object.</summary>
      <param name="watcher">The object to use to return results.</param>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelationships(System.Management.ManagementOperationObserver,System.String)">
      <summary>Gets a collection of associations to the object.</summary>
      <param name="watcher">The object to use to return results.</param>
      <param name="relationshipClass">The associations to include.</param>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelationships(System.Management.ManagementOperationObserver,System.String,System.String,System.String,System.Boolean,System.Management.EnumerationOptions)">
      <summary>Gets a collection of associations to the object.</summary>
      <param name="watcher">The object to use to return results.</param>
      <param name="relationshipClass">The type of relationship of interest.</param>
      <param name="relationshipQualifier">The qualifier to be present on the relationship.</param>
      <param name="thisRole">The role of this object in the relationship.</param>
      <param name="classDefinitionsOnly">When this method returns, it contains only the class definitions for the result set.</param>
      <param name="options">The extended options for the query execution.</param>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelationships(System.String)">
      <summary>Gets a collection of associations to the object.</summary>
      <param name="relationshipClass">The associations to include.</param>
      <returns>A <see cref="T:System.Management.ManagementObjectCollection" /> containing the association objects.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.GetRelationships(System.String,System.String,System.String,System.Boolean,System.Management.EnumerationOptions)">
      <summary>Gets a collection of associations to the object.</summary>
      <param name="relationshipClass">The type of relationship of interest.</param>
      <param name="relationshipQualifier">The qualifier to be present on the relationship.</param>
      <param name="thisRole">The role of this object in the relationship.</param>
      <param name="classDefinitionsOnly">When this method returns, it contains only the class definitions for the result set.</param>
      <param name="options">The extended options for the query execution.</param>
      <returns>A <see cref="T:System.Management.ManagementObjectCollection" /> containing the association objects.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.InvokeMethod(System.Management.ManagementOperationObserver,System.String,System.Management.ManagementBaseObject,System.Management.InvokeMethodOptions)">
      <summary>Invokes a method on the object, asynchronously.</summary>
      <param name="watcher">A <see cref="T:System.Management.ManagementOperationObserver" /> used to handle the asynchronous execution's progress and results.</param>
      <param name="methodName">The name of the method to be executed.</param>
      <param name="inParameters">A <see cref="T:System.Management.ManagementBaseObject" /> containing the input parameters for the method.</param>
      <param name="options">An <see cref="T:System.Management.InvokeMethodOptions" /> containing additional options used to execute the method.</param>
    </member>
    <member name="M:System.Management.ManagementObject.InvokeMethod(System.Management.ManagementOperationObserver,System.String,System.Object[])">
      <summary>Invokes a method on the object, asynchronously.</summary>
      <param name="watcher">The object to receive the results of the operation.</param>
      <param name="methodName">The name of the method to execute.</param>
      <param name="args">An array containing parameter values.</param>
    </member>
    <member name="M:System.Management.ManagementObject.InvokeMethod(System.String,System.Management.ManagementBaseObject,System.Management.InvokeMethodOptions)">
      <summary>Invokes a method on the WMI object. The input and output parameters are represented as <see cref="T:System.Management.ManagementBaseObject" /> objects.</summary>
      <param name="methodName">The name of the method to execute.</param>
      <param name="inParameters">A <see cref="T:System.Management.ManagementBaseObject" /> holding the input parameters to the method.</param>
      <param name="options">An <see cref="T:System.Management.InvokeMethodOptions" /> containing additional options for the execution of the method.</param>
      <returns>A <see cref="T:System.Management.ManagementBaseObject" /> containing the output parameters and return value of the executed method.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.InvokeMethod(System.String,System.Object[])">
      <summary>Invokes a method on the object.</summary>
      <param name="methodName">The name of the method to execute.</param>
      <param name="args">An array containing parameter values.</param>
      <returns>The object value returned by the method.</returns>
    </member>
    <member name="P:System.Management.ManagementObject.Options">
      <summary>Gets or sets additional information to use when retrieving the object.</summary>
      <returns>An <see cref="T:System.Management.ObjectGetOptions" /> to use when retrieving the object.</returns>
    </member>
    <member name="P:System.Management.ManagementObject.Path">
      <summary>Gets or sets the object's WMI path.</summary>
      <returns>A <see cref="T:System.Management.ManagementPath" /> representing the object's path.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.Put">
      <summary>Commits the changes to the object.</summary>
      <returns>A <see cref="T:System.Management.ManagementPath" /> containing the path to the committed object.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.Put(System.Management.ManagementOperationObserver)">
      <summary>Commits the changes to the object, asynchronously.</summary>
      <param name="watcher">A <see cref="T:System.Management.ManagementOperationObserver" /> used to handle the progress and results of the asynchronous operation.</param>
    </member>
    <member name="M:System.Management.ManagementObject.Put(System.Management.ManagementOperationObserver,System.Management.PutOptions)">
      <summary>Commits the changes to the object asynchronously and using the specified options.</summary>
      <param name="watcher">A <see cref="T:System.Management.ManagementOperationObserver" /> used to handle the progress and results of the asynchronous operation.</param>
      <param name="options">A <see cref="T:System.Management.PutOptions" /> used to specify additional options for the commit operation.</param>
    </member>
    <member name="M:System.Management.ManagementObject.Put(System.Management.PutOptions)">
      <summary>Commits the changes to the object.</summary>
      <param name="options">The options for how to commit the changes.</param>
      <returns>A <see cref="T:System.Management.ManagementPath" /> containing the path to the committed object.</returns>
    </member>
    <member name="P:System.Management.ManagementObject.Scope">
      <summary>Gets or sets the scope in which this object resides.</summary>
      <returns>The scope in which this object resides.</returns>
    </member>
    <member name="M:System.Management.ManagementObject.ToString">
      <summary>Returns the full path of the object. This is an override of the default object implementation.</summary>
      <returns>The full path of the object.</returns>
    </member>
    <member name="T:System.Management.ManagementObjectCollection">
      <summary>Represents different collections of management objects retrieved through WMI. The objects in this collection are of <see cref="T:System.Management.ManagementBaseObject" />-derived types, including <see cref="T:System.Management.ManagementObject" /> and <see cref="T:System.Management.ManagementClass" />. The collection can be the result of a WMI query executed through a <see cref="T:System.Management.ManagementObjectSearcher" />, or an enumeration of management objects of a specified type retrieved through a <see cref="T:System.Management.ManagementClass" /> representing that type. In addition, this can be a collection of management objects related in a specified way to a specific management object - in this case the collection would be retrieved through a method such as <see cref="M:System.Management.ManagementObject.GetRelated" />. The collection can be walked using the <see cref="T:System.Management.ManagementObjectCollection.ManagementObjectEnumerator" /> and objects in it can be inspected or manipulated for various management tasks.</summary>
    </member>
    <member name="M:System.Management.ManagementObjectCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the collection to an array.</summary>
      <param name="array">An array to copy to.</param>
      <param name="index">The index to start from.</param>
    </member>
    <member name="M:System.Management.ManagementObjectCollection.CopyTo(System.Management.ManagementBaseObject[],System.Int32)">
      <summary>Copies the items in the collection to a <see cref="T:System.Management.ManagementBaseObject" /> array.</summary>
      <param name="objectCollection">The target array.</param>
      <param name="index">The index to start from.</param>
    </member>
    <member name="P:System.Management.ManagementObjectCollection.Count">
      <summary>Gets a value indicating the number of objects in the collection.</summary>
      <returns>The number of objects in the collection.</returns>
    </member>
    <member name="M:System.Management.ManagementObjectCollection.Dispose">
      <summary>Releases resources associated with this object. After this method has been called, an attempt to use this object will result in an <see cref="T:System.ObjectDisposedException" /> being thrown.</summary>
    </member>
    <member name="M:System.Management.ManagementObjectCollection.Finalize">
      <summary>Disposes of resources the object is holding. This is the destructor for the object. Finalizers are expressed using destructor syntax.</summary>
    </member>
    <member name="M:System.Management.ManagementObjectCollection.GetEnumerator">
      <summary>Returns the enumerator for the collection.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Management.ManagementObjectCollection.IsSynchronized">
      <summary>Gets a value that indicates whether the object is synchronized (thread-safe).</summary>
      <returns>
        <see langword="true" /> if the object is synchronized; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Management.ManagementObjectCollection.ManagementObjectEnumerator">
      <summary>Represents the enumerator on the collection.</summary>
    </member>
    <member name="P:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.Current">
      <summary>Gets the current <see cref="T:System.Management.ManagementBaseObject" /> that this enumerator points to.</summary>
      <returns>The current object in the enumeration.</returns>
    </member>
    <member name="M:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.Dispose">
      <summary>Releases resources associated with this object. After this method has been called, an attempt to use this object will result in an <see cref="T:System.ObjectDisposedException" /> exception being thrown.</summary>
    </member>
    <member name="M:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.Finalize">
      <summary>Disposes of resources the object is holding. This is the destructor for the object. Finalizers are expressed using destructor syntax.</summary>
    </member>
    <member name="M:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.MoveNext">
      <summary>Indicates whether the enumerator has moved to the next object in the enumeration.</summary>
      <returns>
        <see langword="true" />, if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.Reset">
      <summary>Resets the enumerator to the beginning of the collection.</summary>
    </member>
    <member name="P:System.Management.ManagementObjectCollection.ManagementObjectEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current object in the collection.</summary>
      <returns>The current element in the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
    </member>
    <member name="P:System.Management.ManagementObjectCollection.SyncRoot">
      <summary>Gets the object to be used for synchronization.</summary>
      <returns>An object that can be used for synchronization.</returns>
    </member>
    <member name="M:System.Management.ManagementObjectCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> that iterates through the <see cref="T:System.Management.ManagementObjectCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Management.ManagementObjectCollection" />.</returns>
    </member>
    <member name="T:System.Management.ManagementObjectSearcher">
      <summary>Retrieves a collection of management objects based on a specified query. This class is one of the more commonly used entry points to retrieving management information. For example, it can be used to enumerate all disk drives, network adapters, processes and many more management objects on a system, or to query for all network connections that are up, services that are paused, and so on.  When instantiated, an instance of this class takes as input a WMI query represented in an <see cref="T:System.Management.ObjectQuery" /> or its derivatives, and optionally a <see cref="T:System.Management.ManagementScope" /> representing the WMI namespace to execute the query in. It can also take additional advanced options in an <see cref="T:System.Management.EnumerationOptions" />. When the <see cref="M:System.Management.ManagementObjectSearcher.Get" /> method on this object is invoked, the <see cref="T:System.Management.ManagementObjectSearcher" /> executes the given query in the specified scope and returns a collection of management objects that match the query in a <see cref="T:System.Management.ManagementObjectCollection" />.</summary>
    </member>
    <member name="M:System.Management.ManagementObjectSearcher.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObjectSearcher" /> class. After some properties on this object are set, the object can be used to invoke a query for management information. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.Management.ManagementScope,System.Management.ObjectQuery)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObjectSearcher" /> class used to invoke the specified query in the specified scope.</summary>
      <param name="scope">A <see cref="T:System.Management.ManagementScope" /> representing the scope in which to invoke the query.</param>
      <param name="query">An <see cref="T:System.Management.ObjectQuery" /> representing the query to be invoked.</param>
    </member>
    <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.Management.ManagementScope,System.Management.ObjectQuery,System.Management.EnumerationOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObjectSearcher" /> class to be used to invoke the specified query in the specified scope, with the specified options.</summary>
      <param name="scope">A <see cref="T:System.Management.ManagementScope" /> specifying the scope of the query.</param>
      <param name="query">An <see cref="T:System.Management.ObjectQuery" /> specifying the query to be invoked.</param>
      <param name="options">An <see cref="T:System.Management.EnumerationOptions" /> specifying additional options to be used for the query.</param>
    </member>
    <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.Management.ObjectQuery)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObjectSearcher" /> class used to invoke the specified query for management information.</summary>
      <param name="query">An <see cref="T:System.Management.ObjectQuery" /> representing the query to be invoked by the searcher.</param>
    </member>
    <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObjectSearcher" /> class used to invoke the specified query for management information.</summary>
      <param name="queryString">The WMI query to be invoked by the object.</param>
    </member>
    <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObjectSearcher" /> class used to invoke the specified query in the specified scope.</summary>
      <param name="scope">The scope in which to query.</param>
      <param name="queryString">The query to be invoked.</param>
    </member>
    <member name="M:System.Management.ManagementObjectSearcher.#ctor(System.String,System.String,System.Management.EnumerationOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementObjectSearcher" /> class used to invoke the specified query, in the specified scope, and with the specified options.</summary>
      <param name="scope">The scope in which the query should be invoked.</param>
      <param name="queryString">The query to be invoked.</param>
      <param name="options">An <see cref="T:System.Management.EnumerationOptions" /> specifying additional options for the query.</param>
    </member>
    <member name="M:System.Management.ManagementObjectSearcher.Get">
      <summary>Invokes the specified WMI query and returns the resulting collection.</summary>
      <returns>A <see cref="T:System.Management.ManagementObjectCollection" /> containing the objects that match the specified query.</returns>
    </member>
    <member name="M:System.Management.ManagementObjectSearcher.Get(System.Management.ManagementOperationObserver)">
      <summary>Invokes the WMI query asynchronously, and binds to a watcher to deliver the results.</summary>
      <param name="watcher">The watcher that raises events triggered by the operation.</param>
    </member>
    <member name="P:System.Management.ManagementObjectSearcher.Options">
      <summary>Gets or sets the options for how to search for objects.</summary>
      <returns>The options for searching for WMI objects.</returns>
    </member>
    <member name="P:System.Management.ManagementObjectSearcher.Query">
      <summary>Gets or sets the query to be invoked in the searcher (that is, the criteria to be applied to the search for management objects).</summary>
      <returns>The query to be invoked in the searcher.</returns>
    </member>
    <member name="P:System.Management.ManagementObjectSearcher.Scope">
      <summary>Gets or sets the scope in which to look for objects (the scope represents a WMI namespace).</summary>
      <returns>The scope (namespace) in which to look for the WMI objects.</returns>
    </member>
    <member name="T:System.Management.ManagementOperationObserver">
      <summary>Manages asynchronous operations and handles management information and events received asynchronously.</summary>
    </member>
    <member name="M:System.Management.ManagementOperationObserver.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementOperationObserver" /> class. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ManagementOperationObserver.Cancel">
      <summary>Cancels all outstanding operations.</summary>
    </member>
    <member name="E:System.Management.ManagementOperationObserver.Completed">
      <summary>Occurs when an operation has completed.</summary>
    </member>
    <member name="E:System.Management.ManagementOperationObserver.ObjectPut">
      <summary>Occurs when an object has been successfully committed.</summary>
    </member>
    <member name="E:System.Management.ManagementOperationObserver.ObjectReady">
      <summary>Occurs when a new object is available.</summary>
    </member>
    <member name="E:System.Management.ManagementOperationObserver.Progress">
      <summary>Occurs to indicate the progress of an ongoing operation.</summary>
    </member>
    <member name="T:System.Management.ManagementOptions">
      <summary>Provides an abstract base class for all options objects.</summary>
    </member>
    <member name="M:System.Management.ManagementOptions.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="P:System.Management.ManagementOptions.Context">
      <summary>Gets or sets a WMI context object. This is a name-value pairs list to be passed through to a WMI provider that supports context information for customized operation.</summary>
      <returns>Returns a <see cref="T:System.Management.ManagementNamedValueCollection" /> that contains WMI context information.</returns>
    </member>
    <member name="F:System.Management.ManagementOptions.InfiniteTimeout">
      <summary>Indicates that no timeout should occur.</summary>
    </member>
    <member name="P:System.Management.ManagementOptions.Timeout">
      <summary>Gets or sets the time-out to apply to the operation. Note that for operations that return collections, this time-out applies to the enumeration through the resulting collection, not the operation itself (the <see cref="P:System.Management.EnumerationOptions.ReturnImmediately" /> property is used for the latter). This property is used to indicate that the operation should be performed semi-synchronously.</summary>
      <returns>Returns a <see cref="T:System.TimeSpan" /> that defines the time-out time to apply to the operation.</returns>
    </member>
    <member name="T:System.Management.ManagementPath">
      <summary>Provides a wrapper for parsing and building paths to WMI objects.</summary>
    </member>
    <member name="M:System.Management.ManagementPath.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementPath" /> class that is empty. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ManagementPath.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementPath" /> class for the given path.</summary>
      <param name="path">The object path.</param>
    </member>
    <member name="P:System.Management.ManagementPath.ClassName">
      <summary>Gets or sets the class portion of the path.</summary>
      <returns>Returns a <see cref="T:System.String" /> value that holds the class portion of the path.</returns>
    </member>
    <member name="M:System.Management.ManagementPath.Clone">
      <summary>Returns a copy of the <see cref="T:System.Management.ManagementPath" />.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="P:System.Management.ManagementPath.DefaultPath">
      <summary>Gets or sets the default scope path used when no scope is specified. The default scope is \\.\root\cimv2, and can be changed by setting this property.</summary>
      <returns>Returns a <see cref="T:System.Management.ManagementPath" /> that contains the default scope (namespace) path used when no scope is specified.</returns>
    </member>
    <member name="P:System.Management.ManagementPath.IsClass">
      <summary>Gets or sets a value indicating whether this is a class path.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether this is a class path.</returns>
    </member>
    <member name="P:System.Management.ManagementPath.IsInstance">
      <summary>Gets or sets a value indicating whether this is an instance path.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether this is an instance path.</returns>
    </member>
    <member name="P:System.Management.ManagementPath.IsSingleton">
      <summary>Gets or sets a value indicating whether this is a singleton instance path.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether this is a singleton instance path.</returns>
    </member>
    <member name="P:System.Management.ManagementPath.NamespacePath">
      <summary>Gets or sets the namespace part of the path. Note that this does not include the server name, which can be retrieved separately.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the namespace part of the path.</returns>
    </member>
    <member name="P:System.Management.ManagementPath.Path">
      <summary>Gets or sets the string representation of the full path in the object.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the full path.</returns>
    </member>
    <member name="P:System.Management.ManagementPath.RelativePath">
      <summary>Gets or sets the relative path: class name and keys only.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the relative path.</returns>
    </member>
    <member name="P:System.Management.ManagementPath.Server">
      <summary>Gets or sets the server part of the path.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the server name.</returns>
    </member>
    <member name="M:System.Management.ManagementPath.SetAsClass">
      <summary>Sets the path as a new class path. This means that the path must have a class name but not key values.</summary>
    </member>
    <member name="M:System.Management.ManagementPath.SetAsSingleton">
      <summary>Sets the path as a new singleton object path. This means that it is a path to an instance but there are no key values.</summary>
    </member>
    <member name="M:System.Management.ManagementPath.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current instance.</summary>
      <returns>A new object that is a copy of this instance.</returns>
    </member>
    <member name="M:System.Management.ManagementPath.ToString">
      <summary>Returns the full object path as the string representation.</summary>
      <returns>A string containing the full object path represented by this object. This value is equivalent to the value of the <see cref="P:System.Management.ManagementPath.Path" /> property.</returns>
    </member>
    <member name="T:System.Management.ManagementQuery">
      <summary>Provides an abstract base class for all management query objects.</summary>
    </member>
    <member name="M:System.Management.ManagementQuery.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="M:System.Management.ManagementQuery.ParseQuery(System.String)">
      <summary>Parses the query string and sets the property values accordingly. If the query is valid, the class name property and condition property of the query will be parsed.</summary>
      <param name="query">The query string to be parsed.</param>
    </member>
    <member name="P:System.Management.ManagementQuery.QueryLanguage">
      <summary>Gets or sets the query language used in the query string, defining the format of the query string.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the format of the query string.</returns>
    </member>
    <member name="P:System.Management.ManagementQuery.QueryString">
      <summary>Gets or sets the query in text format.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the query.</returns>
    </member>
    <member name="T:System.Management.ManagementScope">
      <summary>Represents a scope (namespace) for management operations.</summary>
    </member>
    <member name="M:System.Management.ManagementScope.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementScope" /> class, with default values. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ManagementScope.#ctor(System.Management.ManagementPath)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementScope" /> class representing the specified scope path.</summary>
      <param name="path">A <see cref="T:System.Management.ManagementPath" /> containing the path to a server and namespace for the <see cref="T:System.Management.ManagementScope" />.</param>
    </member>
    <member name="M:System.Management.ManagementScope.#ctor(System.Management.ManagementPath,System.Management.ConnectionOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementScope" /> class representing the specified scope path, with the specified options.</summary>
      <param name="path">A <see cref="T:System.Management.ManagementPath" /> containing the path to the server and namespace for the <see cref="T:System.Management.ManagementScope" />.</param>
      <param name="options">The <see cref="T:System.Management.ConnectionOptions" /> containing options for the connection.</param>
    </member>
    <member name="M:System.Management.ManagementScope.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementScope" /> class representing the specified scope path.</summary>
      <param name="path">The server and namespace path for the <see cref="T:System.Management.ManagementScope" />.</param>
    </member>
    <member name="M:System.Management.ManagementScope.#ctor(System.String,System.Management.ConnectionOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ManagementScope" /> class representing the specified scope path, with the specified options.</summary>
      <param name="path">The server and namespace for the <see cref="T:System.Management.ManagementScope" />.</param>
      <param name="options">A <see cref="T:System.Management.ConnectionOptions" /> containing options for the connection.</param>
    </member>
    <member name="M:System.Management.ManagementScope.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>A new copy of the <see cref="T:System.Management.ManagementScope" />.</returns>
    </member>
    <member name="M:System.Management.ManagementScope.Connect">
      <summary>Connects this <see cref="T:System.Management.ManagementScope" /> to the actual WMI scope.</summary>
    </member>
    <member name="P:System.Management.ManagementScope.IsConnected">
      <summary>Gets a value indicating whether the <see cref="T:System.Management.ManagementScope" /> is currently bound to a WMI server and namespace.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the scope is currently bound to a WMI server and namespace.</returns>
    </member>
    <member name="P:System.Management.ManagementScope.Options">
      <summary>Gets or sets options for making the WMI connection.</summary>
      <returns>Returns a <see cref="T:System.Management.ConnectionOptions" /> that contains the options for making a WMI connection.</returns>
    </member>
    <member name="P:System.Management.ManagementScope.Path">
      <summary>Gets or sets the path for the <see cref="T:System.Management.ManagementScope" />.</summary>
      <returns>Returns a <see cref="T:System.Management.ManagementPath" /> containing the path to the scope (namespace).</returns>
    </member>
    <member name="M:System.Management.ManagementScope.System#ICloneable#Clone">
      <summary>Creates a new object that is a copy of the current instance.</summary>
      <returns>A new object that is a copy of this instance.</returns>
    </member>
    <member name="T:System.Management.ManagementStatus">
      <summary>Describes the enumeration of all WMI error codes that are currently defined.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.AccessDenied">
      <summary>The current user does not have permission to perform the action.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.AggregatingByObject">
      <summary>A GROUP BY clause references a property that is an embedded object without using dot notation.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.AlreadyExists">
      <summary>In a put operation, the wbemChangeFlagCreateOnly flag was specified, but the instance already exists.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.AmendedObject">
      <summary>An amended object was used in a put operation without the WBEM_FLAG_USE_AMENDED_QUALIFIERS flag being specified.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.BackupRestoreWinmgmtRunning">
      <summary>An request was made to back up or restore the repository while WinMgmt.exe was using it.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.BufferTooSmall">
      <summary>The supplied buffer was too small to hold all the objects in the enumerator or to read a string property.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.CallCanceled">
      <summary>An asynchronous process has been canceled internally or by the user. Note that because of the timing and nature of the asynchronous operation, the operation may not have been truly canceled.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.CannotBeAbstract">
      <summary>The class was made abstract when its superclass is not abstract.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.CannotBeKey">
      <summary>There was an illegal attempt to specify a key qualifier on a property that cannot be a key. The keys are specified in the class definition for an object and cannot be altered on a per-instance basis.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.CannotBeSingleton">
      <summary>An illegal attempt was made to make a class singleton, such as when the class is derived from a non-singleton class.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.CannotChangeIndexInheritance">
      <summary>An attempt was made to change an index when instances or derived classes are already using the index.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.CannotChangeKeyInheritance">
      <summary>An attempt was made to change a key when instances or derived classes are already using the key.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.CircularReference">
      <summary>An attempt has been made to create a reference that is circular (for example, deriving a class from itself).</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ClassHasChildren">
      <summary>An attempt was made to make a change that would invalidate a derived class.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ClassHasInstances">
      <summary>An attempt has been made to delete or modify a class that has instances.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ClientTooSlow">
      <summary>The client was not retrieving objects quickly enough from an enumeration.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.CriticalError">
      <summary>An internal, critical, and unexpected error occurred. Report this error to Microsoft Technical Support.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.Different">
      <summary>The compared items (such as objects and classes) are not identical.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.DuplicateObjects">
      <summary>More than one copy of the same object was detected in the result set of an enumeration.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.Failed">
      <summary>The call failed.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.False">
      <summary>This value is returned when no more objects are available, the number of objects returned is less than the number requested, or at the end of an enumeration. It is also returned when the method is called with a value of 0 for the parameter.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.IllegalNull">
      <summary>A value of null was specified for a property that may not be null, such as one that is marked by a Key, Indexed, or Not_Null qualifier.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.IllegalOperation">
      <summary>The user requested an illegal operation, such as spawning a class from an instance.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.IncompleteClass">
      <summary>The current object is not a valid class definition. Either it is incomplete, or it has not been registered with WMI using <see cref="M:System.Management.ManagementObject.Put" />().</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InitializationFailure">
      <summary>A component, such as a provider, failed to initialize for internal reasons.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidCimType">
      <summary>The CIM type specified is not valid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidClass">
      <summary>The specified class is not valid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidContext">
      <summary>The context object is not valid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidDuplicateParameter">
      <summary>A duplicate parameter has been declared in a CIM method.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidFlavor">
      <summary>The specified flavor was invalid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidMethod">
      <summary>The requested method is not available.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidMethodParameters">
      <summary>The parameters provided for the method are not valid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidNamespace">
      <summary>The specified namespace could not be found.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidObject">
      <summary>The specified instance is not valid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidObjectPath">
      <summary>The specified object path was invalid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidOperation">
      <summary>The requested operation is not valid. This error usually applies to invalid attempts to delete classes or properties.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidOperator">
      <summary>The operator is not valid for this property type.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidParameter">
      <summary>One of the parameters to the call is not correct.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidParameterID">
      <summary>A method parameter has an invalid ID qualifier.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidProperty">
      <summary>The property type is not recognized.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidPropertyType">
      <summary>The CIM type specified for a property is not valid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidProviderRegistration">
      <summary>A provider referenced in the schema has an incorrect or incomplete registration.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidQualifier">
      <summary>An attempt has been made to mismatch qualifiers, such as putting [ManagementKey] on an object instead of a property.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidQualifierType">
      <summary>The value provided for a qualifier was not a legal qualifier type.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidQuery">
      <summary>The query was not syntactically valid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidQueryType">
      <summary>The requested query language is not supported.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidStream">
      <summary>One or more network packets were corrupted during a remote session.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidSuperclass">
      <summary>The specified superclass is not valid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.InvalidSyntax">
      <summary>Reserved for future use.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.LocalCredentials">
      <summary>The user specified a user name, password, or authority on a local connection. The user must use an empty user name and password and rely on default security.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.MarshalInvalidSignature">
      <summary>The packet is corrupted.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.MarshalVersionMismatch">
      <summary>The packet has an unsupported version.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.MethodDisabled">
      <summary>An attempt was made to execute a method marked with [disabled].</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.MethodNotImplemented">
      <summary>An attempt was made to execute a method not marked with [implemented] in any relevant class.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.MissingAggregationList">
      <summary>A GROUP BY clause was used. Aggregation on all properties is not supported.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.MissingGroupWithin">
      <summary>A GROUP BY clause was used without the corresponding GROUP WITHIN clause.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.MissingParameterID">
      <summary>A parameter was missing from the method call.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.NoError">
      <summary>The operation was successful.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.NoMoreData">
      <summary>No more data is available from the enumeration; the user should terminate the enumeration.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.NonconsecutiveParameterIDs">
      <summary>One or more of the method parameters have ID qualifiers that are out of sequence.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.NondecoratedObject">
      <summary>Reserved for future use.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.NotAvailable">
      <summary>The resource, typically a remote server, is not currently available.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.NotEventClass">
      <summary>The FROM clause of a filtering query references a class that is not an event class.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.NotFound">
      <summary>The object could not be found.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.NotSupported">
      <summary>The feature or operation is not supported.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.OperationCanceled">
      <summary>The operation was canceled.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.OutOfDiskSpace">
      <summary>There is not enough free disk space to continue the operation.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.OutOfMemory">
      <summary>There was not enough memory for the operation.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.OverrideNotAllowed">
      <summary>The add operation cannot be performed on the qualifier because the owning object does not permit overrides.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ParameterIDOnRetval">
      <summary>The return value for a method has an ID qualifier.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.PartialResults">
      <summary>The user did not receive all of the requested objects because of inaccessible resources (other than security violations).</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.Pending">
      <summary>A request is still in progress; however, the results are not yet available.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.PrivilegeNotHeld">
      <summary>The operation failed because the client did not have the necessary security privilege.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.PropagatedMethod">
      <summary>An attempt was made to reuse an existing method name from a superclass, and the signatures did not match.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.PropagatedProperty">
      <summary>The user attempted to delete a property that was not owned. The property was inherited from a parent class.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.PropagatedQualifier">
      <summary>The user attempted to delete a qualifier that was not owned. The qualifier was inherited from a parent class.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.PropertyNotAnObject">
      <summary>Dot notation was used on a property that is not an embedded object.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ProviderFailure">
      <summary>The provider failed after initialization.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ProviderLoadFailure">
      <summary>COM cannot locate a provider referenced in the schema.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ProviderNotCapable">
      <summary>The provider cannot perform the requested operation, such as requesting a query that is too complex, retrieving an instance, creating or updating a class, deleting a class, or enumerating a class.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ProviderNotFound">
      <summary>A provider referenced in the schema does not have a corresponding registration.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.QueryNotImplemented">
      <summary>Reserved for future use.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.QueueOverflow">
      <summary>The asynchronous delivery queue overflowed from the event consumer being too slow.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ReadOnly">
      <summary>The property that you are attempting to modify is read-only.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.RefresherBusy">
      <summary>The refresher is busy with another operation.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.RegistrationTooBroad">
      <summary>The provider registration overlaps with the system event domain.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.RegistrationTooPrecise">
      <summary>A WITHIN clause was not used in this query.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ResetToDefault">
      <summary>An overridden property was deleted. This value is returned to signal that the original, non-overridden value has been restored as a result of the deletion.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ServerTooBusy">
      <summary>The delivery of an event has failed. The provider may choose to re-raise the event.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ShuttingDown">
      <summary>The user has requested an operation while WMI is in the process of closing.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.SystemProperty">
      <summary>There was an attempt to get qualifiers on a system property.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.Timedout">
      <summary>A call timed out. This is not an error condition; therefore, some results may have been returned.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.TooManyProperties">
      <summary>An attempt was made to create more properties than the current version of the class supports.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.TooMuchData">
      <summary>Reserved for future use.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.TransportFailure">
      <summary>A networking error that prevents normal operation has occurred.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.TypeMismatch">
      <summary>A type mismatch occurred.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.Unexpected">
      <summary>The client made an unexpected and illegal sequence of calls.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.UninterpretableProviderQuery">
      <summary>An event provider registration query (__EventProviderRegistration) did not specify the classes for which events were provided.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.UnknownObjectType">
      <summary>An object with an incorrect type or version was encountered during marshaling.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.UnknownPacketType">
      <summary>A packet with an incorrect type or version was encountered during marshaling.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.UnparsableQuery">
      <summary>The filtering query is syntactically invalid.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.UnsupportedClassUpdate">
      <summary>The specified class is not supported.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.UnsupportedParameter">
      <summary>One or more parameter values, such as a query text, is too complex or unsupported. WMI is requested to retry the operation with simpler parameters.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.UnsupportedPutExtension">
      <summary>The provider does not support the requested put operation.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.UpdateOverrideNotAllowed">
      <summary>An attempt was made in a derived class to override a non-overridable qualifier.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.UpdatePropagatedMethod">
      <summary>A method was redeclared with a conflicting signature in a derived class.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.UpdateTypeMismatch">
      <summary>A property was redefined with a conflicting type in a derived class.</summary>
    </member>
    <member name="F:System.Management.ManagementStatus.ValueOutOfRange">
      <summary>The request was made with an out-of-range value, or is incompatible with the type.</summary>
    </member>
    <member name="T:System.Management.MethodData">
      <summary>Contains information about a WMI method.</summary>
    </member>
    <member name="P:System.Management.MethodData.InParameters">
      <summary>Gets the input parameters to the method. Each parameter is described as a property in the object. If a parameter is both in and out, it appears in both the <see cref="P:System.Management.MethodData.InParameters" /> and <see cref="P:System.Management.MethodData.OutParameters" /> properties.</summary>
      <returns>Returns a <see cref="T:System.Management.ManagementBaseObject" /> containing the input parameters to the method.</returns>
    </member>
    <member name="P:System.Management.MethodData.Name">
      <summary>Gets the name of the method.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the name of the method.</returns>
    </member>
    <member name="P:System.Management.MethodData.Origin">
      <summary>Gets the name of the management class in which the method was first introduced in the class inheritance hierarchy.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the name of the class in which the method was first introduced in the class inheritance hierarchy.</returns>
    </member>
    <member name="P:System.Management.MethodData.OutParameters">
      <summary>Gets the output parameters to the method. Each parameter is described as a property in the object. If a parameter is both in and out, it will appear in both the <see cref="P:System.Management.MethodData.InParameters" /> and <see cref="P:System.Management.MethodData.OutParameters" /> properties.</summary>
      <returns>Returns a <see cref="T:System.Management.ManagementBaseObject" /> containing the output parameters for the method.</returns>
    </member>
    <member name="P:System.Management.MethodData.Qualifiers">
      <summary>Gets a collection of qualifiers defined in the method. Each element is of type <see cref="T:System.Management.QualifierData" /> and contains information such as the qualifier name, value, and flavor.</summary>
      <returns>Returns a <see cref="T:System.Management.QualifierDataCollection" /> containing the qualifiers for the method.</returns>
    </member>
    <member name="T:System.Management.MethodDataCollection">
      <summary>Represents the set of methods available in the collection.</summary>
    </member>
    <member name="M:System.Management.MethodDataCollection.Add(System.String)">
      <summary>Adds a <see cref="T:System.Management.MethodData" /> to the <see cref="T:System.Management.MethodDataCollection" />. This overload will add a new method with no parameters to the collection.</summary>
      <param name="methodName">The name of the method to add.</param>
    </member>
    <member name="M:System.Management.MethodDataCollection.Add(System.String,System.Management.ManagementBaseObject,System.Management.ManagementBaseObject)">
      <summary>Adds a <see cref="T:System.Management.MethodData" /> to the <see cref="T:System.Management.MethodDataCollection" />. This overload will add a new method with the specified parameter objects to the collection.</summary>
      <param name="methodName">The name of the method to add.</param>
      <param name="inParameters">The <see cref="T:System.Management.ManagementBaseObject" /> holding the input parameters to the method.</param>
      <param name="outParameters">The <see cref="T:System.Management.ManagementBaseObject" /> holding the output parameters to the method.</param>
    </member>
    <member name="M:System.Management.MethodDataCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the <see cref="T:System.Management.MethodDataCollection" /> into an array.</summary>
      <param name="array">The array to which to copy the collection.</param>
      <param name="index">The index from which to start.</param>
    </member>
    <member name="M:System.Management.MethodDataCollection.CopyTo(System.Management.MethodData[],System.Int32)">
      <summary>Copies the <see cref="T:System.Management.MethodDataCollection" /> to a specialized <see cref="T:System.Management.MethodData" /> array.</summary>
      <param name="methodArray">The destination array to which to copy the <see cref="T:System.Management.MethodData" /> objects.</param>
      <param name="index">The index in the destination array from which to start the copy.</param>
    </member>
    <member name="P:System.Management.MethodDataCollection.Count">
      <summary>Gets the number of objects in the <see cref="T:System.Management.MethodDataCollection" /> collection.</summary>
      <returns>Returns an <see cref="T:System.Int32" /> value representing the number of objects in the collection.</returns>
    </member>
    <member name="M:System.Management.MethodDataCollection.GetEnumerator">
      <summary>Returns an enumerator for the <see cref="T:System.Management.MethodDataCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> to enumerate through the collection.</returns>
    </member>
    <member name="P:System.Management.MethodDataCollection.IsSynchronized">
      <summary>Gets a value that indicates whether the object is synchronized.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the object is synchronized.</returns>
    </member>
    <member name="P:System.Management.MethodDataCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:System.Management.MethodData" /> from the <see cref="T:System.Management.MethodDataCollection" />.</summary>
      <param name="methodName">The name of the method requested.</param>
      <returns>Returns a <see cref="T:System.Management.MethodData" /> containing the method data for a specified method from the collection.</returns>
    </member>
    <member name="T:System.Management.MethodDataCollection.MethodDataEnumerator">
      <summary>Represents the enumerator for <see cref="T:System.Management.MethodData" /> objects in the <see cref="T:System.Management.MethodDataCollection" />.</summary>
    </member>
    <member name="P:System.Management.MethodDataCollection.MethodDataEnumerator.Current">
      <summary>Returns the current <see cref="T:System.Management.MethodData" /> in the <see cref="T:System.Management.MethodDataCollection" /> enumeration.</summary>
      <returns>The current <see cref="T:System.Management.MethodData" /> item in the collection.</returns>
    </member>
    <member name="M:System.Management.MethodDataCollection.MethodDataEnumerator.MoveNext">
      <summary>Moves to the next element in the <see cref="T:System.Management.MethodDataCollection" /> enumeration.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next method; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Management.MethodDataCollection.MethodDataEnumerator.Reset">
      <summary>Resets the enumerator to the beginning of the <see cref="T:System.Management.MethodDataCollection" /> enumeration.</summary>
    </member>
    <member name="P:System.Management.MethodDataCollection.MethodDataEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current object in the collection.</summary>
      <returns>Returns the current element in the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
    </member>
    <member name="M:System.Management.MethodDataCollection.Remove(System.String)">
      <summary>Removes a <see cref="T:System.Management.MethodData" /> from the <see cref="T:System.Management.MethodDataCollection" />.</summary>
      <param name="methodName">The name of the method to remove from the collection.</param>
    </member>
    <member name="P:System.Management.MethodDataCollection.SyncRoot">
      <summary>Gets the object to be used for synchronization.</summary>
      <returns>Returns an <see cref="T:System.Object" /> value representing the object to be used for synchronization.</returns>
    </member>
    <member name="M:System.Management.MethodDataCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> that iterates through the <see cref="T:System.Management.MethodDataCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Management.MethodDataCollection" />.</returns>
    </member>
    <member name="T:System.Management.ObjectGetOptions">
      <summary>Specifies options for getting a management object.</summary>
    </member>
    <member name="M:System.Management.ObjectGetOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ObjectGetOptions" /> class for getting a WMI object, using default values. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ObjectGetOptions.#ctor(System.Management.ManagementNamedValueCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ObjectGetOptions" /> class for getting a WMI object, using the specified provider-specific context.</summary>
      <param name="context">A provider-specific, named-value pairs context object to be passed through to the provider.</param>
    </member>
    <member name="M:System.Management.ObjectGetOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ObjectGetOptions" /> class for getting a WMI object, using the given options values.</summary>
      <param name="context">A provider-specific, named-value pairs context object to be passed through to the provider.</param>
      <param name="timeout">The length of time to let the operation perform before it times out. The default is <see cref="F:System.TimeSpan.MaxValue" />.</param>
      <param name="useAmendedQualifiers">
        <see langword="true" /> if the returned objects should contain amended (locale-aware) qualifiers; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Management.ObjectGetOptions.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="P:System.Management.ObjectGetOptions.UseAmendedQualifiers">
      <summary>Gets or sets a value indicating whether the objects returned from WMI should contain amended information. Typically, amended information is localizable information attached to the WMI object, such as object and property descriptions.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the objects returned from WMI should contain amended information.</returns>
    </member>
    <member name="T:System.Management.ObjectPutEventArgs">
      <summary>Holds event data for the <see cref="E:System.Management.ManagementOperationObserver.ObjectPut" /> event.</summary>
    </member>
    <member name="P:System.Management.ObjectPutEventArgs.Path">
      <summary>Gets the identity of the object that has been put.</summary>
      <returns>Returns a <see cref="T:System.Management.ManagementPath" /> containing the path of the object that has been put.</returns>
    </member>
    <member name="T:System.Management.ObjectPutEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Management.ManagementOperationObserver.ObjectPut" /> event.</summary>
      <param name="sender">The instance of the object for which to invoke this method.</param>
      <param name="e">The <see cref="T:System.Management.ObjectPutEventArgs" /> that specifies the reason the event was invoked.</param>
    </member>
    <member name="T:System.Management.ObjectQuery">
      <summary>Represents a management query that returns instances or classes.</summary>
    </member>
    <member name="M:System.Management.ObjectQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ObjectQuery" /> class with no initialized values. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.ObjectQuery.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ObjectQuery" /> class for a specific query string.</summary>
      <param name="query">The string representation of the query.</param>
    </member>
    <member name="M:System.Management.ObjectQuery.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.ObjectQuery" /> class for a specific query string and language.</summary>
      <param name="language">The query language in which this query is specified.</param>
      <param name="query">The string representation of the query.</param>
    </member>
    <member name="M:System.Management.ObjectQuery.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="T:System.Management.ObjectReadyEventArgs">
      <summary>Holds event data for the <see cref="E:System.Management.ManagementOperationObserver.ObjectReady" /> event.</summary>
    </member>
    <member name="P:System.Management.ObjectReadyEventArgs.NewObject">
      <summary>Gets the newly-returned object.</summary>
      <returns>Returns a <see cref="T:System.Management.ManagementBaseObject" /> containing the newly-returned object.</returns>
    </member>
    <member name="T:System.Management.ObjectReadyEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Management.ManagementOperationObserver.ObjectReady" /> event.</summary>
      <param name="sender">The instance of the object for which to invoke this method.</param>
      <param name="e">The <see cref="T:System.Management.ObjectReadyEventArgs" /> that specifies the reason the event was invoked.</param>
    </member>
    <member name="T:System.Management.ProgressEventArgs">
      <summary>Holds event data for the <see cref="E:System.Management.ManagementOperationObserver.Progress" /> event.</summary>
    </member>
    <member name="P:System.Management.ProgressEventArgs.Current">
      <summary>Gets the current amount of work done by the operation. This is always less than or equal to <see cref="P:System.Management.ProgressEventArgs.UpperBound" />.</summary>
      <returns>Returns an <see cref="T:System.Int32" /> value representing the current amount of work already completed by the operation.</returns>
    </member>
    <member name="P:System.Management.ProgressEventArgs.Message">
      <summary>Gets or sets optional additional information regarding the operation's progress.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing information regarding the operation's progress.</returns>
    </member>
    <member name="P:System.Management.ProgressEventArgs.UpperBound">
      <summary>Gets the total amount of work required to be done by the operation.</summary>
      <returns>Returns an <see cref="T:System.Int32" /> value representing the total amount of work to be done by the operation.</returns>
    </member>
    <member name="T:System.Management.ProgressEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Management.ManagementOperationObserver.Progress" /> event.</summary>
      <param name="sender">The instance of the object for which to invoke this method.</param>
      <param name="e">The <see cref="T:System.Management.ProgressEventArgs" /> that specifies the reason the event was invoked.</param>
    </member>
    <member name="T:System.Management.PropertyData">
      <summary>Represents information about a WMI property.</summary>
    </member>
    <member name="P:System.Management.PropertyData.IsArray">
      <summary>Gets a value indicating whether the property is an array.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the property is an array.</returns>
    </member>
    <member name="P:System.Management.PropertyData.IsLocal">
      <summary>Gets a value indicating whether the property has been defined in the current WMI class.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the property has been defined in the current WMI class.</returns>
    </member>
    <member name="P:System.Management.PropertyData.Name">
      <summary>Gets the name of the property.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the property name.</returns>
    </member>
    <member name="P:System.Management.PropertyData.Origin">
      <summary>Gets the name of the WMI class in the hierarchy in which the property was introduced.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the name of the WMI class in the hierarchy in which the property was introduced.</returns>
    </member>
    <member name="P:System.Management.PropertyData.Qualifiers">
      <summary>Gets the set of qualifiers defined on the property.</summary>
      <returns>Returns a <see cref="T:System.Management.QualifierDataCollection" /> containing the set of qualifiers defined on the property.</returns>
    </member>
    <member name="P:System.Management.PropertyData.Type">
      <summary>Gets the CIM type of the property.</summary>
      <returns>Returns a <see cref="T:System.Management.CimType" /> enumeration value representing the CIM type of the property.</returns>
    </member>
    <member name="P:System.Management.PropertyData.Value">
      <summary>Gets or sets the current value of the property.</summary>
      <returns>Returns an <see cref="T:System.Object" /> value representing the value of the property.</returns>
    </member>
    <member name="T:System.Management.PropertyDataCollection">
      <summary>Represents the set of properties of a WMI object.</summary>
    </member>
    <member name="M:System.Management.PropertyDataCollection.Add(System.String,System.Management.CimType,System.Boolean)">
      <summary>Adds a new <see cref="T:System.Management.PropertyData" /> with no assigned value.</summary>
      <param name="propertyName">The name of the property.</param>
      <param name="propertyType">The Common Information Model (CIM) type of the property.</param>
      <param name="isArray">
        <see langword="true" /> to specify that the property is an array type; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Management.PropertyDataCollection.Add(System.String,System.Object)">
      <summary>Adds a new <see cref="T:System.Management.PropertyData" /> with the specified value. The value cannot be null and must be convertible to a Common Information Model (CIM) type.</summary>
      <param name="propertyName">The name of the new property.</param>
      <param name="propertyValue">The value of the property (cannot be null).</param>
    </member>
    <member name="M:System.Management.PropertyDataCollection.Add(System.String,System.Object,System.Management.CimType)">
      <summary>Adds a new <see cref="T:System.Management.PropertyData" /> with the specified value and Common Information Model (CIM) type.</summary>
      <param name="propertyName">The name of the property.</param>
      <param name="propertyValue">The value of the property (which can be null).</param>
      <param name="propertyType">The CIM type of the property.</param>
    </member>
    <member name="M:System.Management.PropertyDataCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the <see cref="T:System.Management.PropertyDataCollection" /> into an array.</summary>
      <param name="array">The array to which to copy the <see cref="T:System.Management.PropertyDataCollection" />.</param>
      <param name="index">The index from which to start copying.</param>
    </member>
    <member name="M:System.Management.PropertyDataCollection.CopyTo(System.Management.PropertyData[],System.Int32)">
      <summary>Copies the <see cref="T:System.Management.PropertyDataCollection" /> to a specialized <see cref="T:System.Management.PropertyData" /> object array.</summary>
      <param name="propertyArray">The destination array to contain the copied <see cref="T:System.Management.PropertyDataCollection" />.</param>
      <param name="index">The index in the destination array from which to start copying.</param>
    </member>
    <member name="P:System.Management.PropertyDataCollection.Count">
      <summary>Gets the number of objects in the <see cref="T:System.Management.PropertyDataCollection" />.</summary>
      <returns>Returns an <see cref="T:System.Int32" /> value representing the number of objects in the collection.</returns>
    </member>
    <member name="M:System.Management.PropertyDataCollection.GetEnumerator">
      <summary>Returns the enumerator for this <see cref="T:System.Management.PropertyDataCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Management.PropertyDataCollection.IsSynchronized">
      <summary>Gets a value indicating whether the object is synchronized.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the object is synchronized.</returns>
    </member>
    <member name="P:System.Management.PropertyDataCollection.Item(System.String)">
      <summary>Gets the specified property from the <see cref="T:System.Management.PropertyDataCollection" />, using [] syntax. This property is the indexer for the <see cref="T:System.Management.PropertyDataCollection" /> class.</summary>
      <param name="propertyName">The name of the property to retrieve.</param>
      <returns>Returns a <see cref="T:System.Management.PropertyData" /> containing the data for a specified property in the collection.</returns>
    </member>
    <member name="T:System.Management.PropertyDataCollection.PropertyDataEnumerator">
      <summary>Represents the enumerator for <see cref="T:System.Management.PropertyData" /> objects in the <see cref="T:System.Management.PropertyDataCollection" />.</summary>
    </member>
    <member name="P:System.Management.PropertyDataCollection.PropertyDataEnumerator.Current">
      <summary>Gets the current <see cref="T:System.Management.PropertyData" /> in the <see cref="T:System.Management.PropertyDataCollection" /> enumeration.</summary>
      <returns>The current <see cref="T:System.Management.PropertyData" /> element in the collection.</returns>
    </member>
    <member name="M:System.Management.PropertyDataCollection.PropertyDataEnumerator.MoveNext">
      <summary>Moves to the next element in the <see cref="T:System.Management.PropertyDataCollection" /> enumeration.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Management.PropertyDataCollection.PropertyDataEnumerator.Reset">
      <summary>Resets the enumerator to the beginning of the <see cref="T:System.Management.PropertyDataCollection" /> enumeration.</summary>
    </member>
    <member name="P:System.Management.PropertyDataCollection.PropertyDataEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current object in the collection.</summary>
      <returns>Returns the current element in the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
    </member>
    <member name="M:System.Management.PropertyDataCollection.Remove(System.String)">
      <summary>Removes a <see cref="T:System.Management.PropertyData" /> from the <see cref="T:System.Management.PropertyDataCollection" />.</summary>
      <param name="propertyName">The name of the property to be removed.</param>
    </member>
    <member name="P:System.Management.PropertyDataCollection.SyncRoot">
      <summary>Gets the object to be used for synchronization.</summary>
      <returns>Returns an <see cref="T:System.Object" /> value containing the object to be used for synchronization.</returns>
    </member>
    <member name="M:System.Management.PropertyDataCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> that iterates through the <see cref="T:System.Management.PropertyDataCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Management.PropertyDataCollection" />.</returns>
    </member>
    <member name="T:System.Management.PutOptions">
      <summary>Specifies options for committing management object changes.</summary>
    </member>
    <member name="M:System.Management.PutOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.PutOptions" /> class for put operations, using default values. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.PutOptions.#ctor(System.Management.ManagementNamedValueCollection)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.PutOptions" /> class for committing a WMI object, using the specified provider-specific context.</summary>
      <param name="context">A provider-specific, named-value pairs context object to be passed through to the provider.</param>
    </member>
    <member name="M:System.Management.PutOptions.#ctor(System.Management.ManagementNamedValueCollection,System.TimeSpan,System.Boolean,System.Management.PutType)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.PutOptions" /> class for committing a WMI object, using the specified option values.</summary>
      <param name="context">A provider-specific, named-value pairs object to be passed through to the provider.</param>
      <param name="timeout">The length of time to let the operation perform before it times out. The default is <see cref="F:System.TimeSpan.MaxValue" />.</param>
      <param name="useAmendedQualifiers">
        <see langword="true" /> if the returned objects should contain amended (locale-aware) qualifiers; otherwise, <see langword="false" />.</param>
      <param name="putType">The type of commit to be performed (update or create).</param>
    </member>
    <member name="M:System.Management.PutOptions.Clone">
      <summary>Returns a copy of the object.</summary>
      <returns>The cloned object.</returns>
    </member>
    <member name="P:System.Management.PutOptions.Type">
      <summary>Gets or sets the type of commit to be performed for the object.</summary>
      <returns>One of the enumeration values that indicates the type of commit to be performed for the object.</returns>
    </member>
    <member name="P:System.Management.PutOptions.UseAmendedQualifiers">
      <summary>Gets or sets a value indicating whether the objects returned from WMI should contain amended information. Typically, amended information is localizable information attached to the WMI object, such as object and property descriptions.</summary>
      <returns>
        <see langword="true" /> if the objects returned from WMI should contain amended information; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="T:System.Management.PutType">
      <summary>Describes the possible effects of saving an object to WMI when using <see cref="M:System.Management.ManagementObject.Put" />.</summary>
    </member>
    <member name="F:System.Management.PutType.CreateOnly">
      <summary>Creates an object only; does not update an existing object.</summary>
    </member>
    <member name="F:System.Management.PutType.None">
      <summary>No change.</summary>
    </member>
    <member name="F:System.Management.PutType.UpdateOnly">
      <summary>Updates an existing object only; does not create a new object.</summary>
    </member>
    <member name="F:System.Management.PutType.UpdateOrCreate">
      <summary>Saves the object, whether updating an existing object or creating a new object.</summary>
    </member>
    <member name="T:System.Management.QualifierData">
      <summary>Contains information about a WMI qualifier.</summary>
    </member>
    <member name="P:System.Management.QualifierData.IsAmended">
      <summary>Gets or sets a value indicating whether the qualifier is amended.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the qualifier is amended.</returns>
    </member>
    <member name="P:System.Management.QualifierData.IsLocal">
      <summary>Gets a value indicating whether the qualifier has been defined locally on this class or has been propagated from a base class.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the qualifier has been defined locally on this class or has been propagated from a base class.</returns>
    </member>
    <member name="P:System.Management.QualifierData.IsOverridable">
      <summary>Gets or sets a value indicating whether the value of the qualifier can be overridden when propagated.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the value of the qualifier can be overridden when propagated.</returns>
    </member>
    <member name="P:System.Management.QualifierData.Name">
      <summary>Represents the name of the qualifier.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the name of the qualifier.</returns>
    </member>
    <member name="P:System.Management.QualifierData.PropagatesToInstance">
      <summary>Gets or sets a value indicating whether the qualifier should be propagated to instances of the class.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the qualifier should be propagated to instances of the class.</returns>
    </member>
    <member name="P:System.Management.QualifierData.PropagatesToSubclass">
      <summary>Gets or sets a value indicating whether the qualifier should be propagated to subclasses of the class.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the qualifier should be propagated to subclasses of the class.</returns>
    </member>
    <member name="P:System.Management.QualifierData.Value">
      <summary>Gets or sets the value of the qualifier.</summary>
      <returns>Returns an <see cref="T:System.Object" /> value containing the value of the qualifier.</returns>
    </member>
    <member name="T:System.Management.QualifierDataCollection">
      <summary>Represents a collection of <see cref="T:System.Management.QualifierData" /> objects.</summary>
    </member>
    <member name="M:System.Management.QualifierDataCollection.Add(System.String,System.Object)">
      <summary>Adds a <see cref="T:System.Management.QualifierData" /> to the <see cref="T:System.Management.QualifierDataCollection" />. This overload specifies the qualifier name and value.</summary>
      <param name="qualifierName">The name of the <see cref="T:System.Management.QualifierData" /> to be added to the <see cref="T:System.Management.QualifierDataCollection" />.</param>
      <param name="qualifierValue">The value for the new qualifier.</param>
    </member>
    <member name="M:System.Management.QualifierDataCollection.Add(System.String,System.Object,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>Adds a <see cref="T:System.Management.QualifierData" /> to the <see cref="T:System.Management.QualifierDataCollection" />. This overload specifies all property values for a <see cref="T:System.Management.QualifierData" />.</summary>
      <param name="qualifierName">The qualifier name.</param>
      <param name="qualifierValue">The qualifier value.</param>
      <param name="isAmended">
        <see langword="true" /> to specify that this qualifier is amended (<c>flavor</c>); otherwise, <see langword="false" />.</param>
      <param name="propagatesToInstance">
        <see langword="true" /> to propagate this qualifier to instances; otherwise, <see langword="false" />.</param>
      <param name="propagatesToSubclass">
        <see langword="true" /> to propagate this qualifier to subclasses; otherwise, <see langword="false" />.</param>
      <param name="isOverridable">
        <see langword="true" /> to specify that this qualifier's value is overridable in instances of subclasses; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Management.QualifierDataCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copies the <see cref="T:System.Management.QualifierDataCollection" /> into an array.</summary>
      <param name="array">The array to which to copy the <see cref="T:System.Management.QualifierDataCollection" />.</param>
      <param name="index">The index from which to start copying.</param>
    </member>
    <member name="M:System.Management.QualifierDataCollection.CopyTo(System.Management.QualifierData[],System.Int32)">
      <summary>Copies the <see cref="T:System.Management.QualifierDataCollection" /> into a specialized              <see cref="T:System.Management.QualifierData" /> array.</summary>
      <param name="qualifierArray">The specialized array of <see cref="T:System.Management.QualifierData" /> objects to which to copy the <see cref="T:System.Management.QualifierDataCollection" />.</param>
      <param name="index">The index from which to start copying.</param>
    </member>
    <member name="P:System.Management.QualifierDataCollection.Count">
      <summary>Gets the number of <see cref="T:System.Management.QualifierData" /> objects in the <see cref="T:System.Management.QualifierDataCollection" />.</summary>
      <returns>The number of objects in the collection.</returns>
    </member>
    <member name="M:System.Management.QualifierDataCollection.GetEnumerator">
      <summary>Returns an enumerator for the <see cref="T:System.Management.QualifierDataCollection" />. This method is strongly typed.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> that can be used to iterate through the collection.</returns>
    </member>
    <member name="P:System.Management.QualifierDataCollection.IsSynchronized">
      <summary>Gets a value indicating whether the object is synchronized (thread-safe).</summary>
      <returns>
        <see langword="true" /> if the object is synchronized; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Management.QualifierDataCollection.Item(System.String)">
      <summary>Gets the specified <see cref="T:System.Management.QualifierData" /> from the <see cref="T:System.Management.QualifierDataCollection" />.</summary>
      <param name="qualifierName">The name of the <see cref="T:System.Management.QualifierData" /> to access in the <see cref="T:System.Management.QualifierDataCollection" />.</param>
      <returns>The data for a specified qualifier in the collection.</returns>
    </member>
    <member name="T:System.Management.QualifierDataCollection.QualifierDataEnumerator">
      <summary>Represents the enumerator for <see cref="T:System.Management.QualifierData" /> objects in the <see cref="T:System.Management.QualifierDataCollection" />.</summary>
    </member>
    <member name="P:System.Management.QualifierDataCollection.QualifierDataEnumerator.Current">
      <summary>Gets or sets the current <see cref="T:System.Management.QualifierData" /> in the <see cref="T:System.Management.QualifierDataCollection" /> enumeration.</summary>
      <returns>The current <see cref="T:System.Management.QualifierData" /> element in the collection.</returns>
    </member>
    <member name="M:System.Management.QualifierDataCollection.QualifierDataEnumerator.MoveNext">
      <summary>Moves to the next element in the <see cref="T:System.Management.QualifierDataCollection" /> enumeration.</summary>
      <returns>
        <see langword="true" /> if the enumerator was successfully advanced to the next element; <see langword="false" /> if the enumerator has passed the end of the collection.</returns>
    </member>
    <member name="M:System.Management.QualifierDataCollection.QualifierDataEnumerator.Reset">
      <summary>Resets the enumerator to the beginning of the <see cref="T:System.Management.QualifierDataCollection" /> enumeration.</summary>
    </member>
    <member name="P:System.Management.QualifierDataCollection.QualifierDataEnumerator.System#Collections#IEnumerator#Current">
      <summary>Gets the current object in the collection.</summary>
      <returns>The current element in the collection.</returns>
      <exception cref="T:System.InvalidOperationException">The collection was modified after the enumerator was created.</exception>
    </member>
    <member name="M:System.Management.QualifierDataCollection.Remove(System.String)">
      <summary>Removes a <see cref="T:System.Management.QualifierData" /> from the <see cref="T:System.Management.QualifierDataCollection" /> by name.</summary>
      <param name="qualifierName">The name of the <see cref="T:System.Management.QualifierData" /> to remove.</param>
    </member>
    <member name="P:System.Management.QualifierDataCollection.SyncRoot">
      <summary>Gets the object to be used for synchronization.</summary>
      <returns>The object to be used for synchronization.</returns>
    </member>
    <member name="M:System.Management.QualifierDataCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Returns an <see cref="T:System.Collections.IEnumerator" /> that iterates through the <see cref="T:System.Management.QualifierDataCollection" />.</summary>
      <returns>An <see cref="T:System.Collections.IEnumerator" /> for the <see cref="T:System.Management.QualifierDataCollection" />.</returns>
    </member>
    <member name="T:System.Management.RelatedObjectQuery">
      <summary>Represents a WQL ASSOCIATORS OF data query. It can be used for both instances and schema queries.</summary>
    </member>
    <member name="M:System.Management.RelatedObjectQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.RelatedObjectQuery" /> class. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.RelatedObjectQuery.#ctor(System.Boolean,System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.RelatedObjectQuery" /> class for a schema query using the given set of parameters. This constructor is used for schema queries only: the first parameter must be set to <see langword="true" /> .</summary>
      <param name="isSchemaQuery">
        <see langword="true" /> to indicate that this is a schema query; otherwise, <see langword="false" /> .</param>
      <param name="sourceObject">The path of the source class.</param>
      <param name="relatedClass">The related objects' required base class.</param>
      <param name="relationshipClass">The relationship type.</param>
      <param name="relatedQualifier">The qualifier required to be present on the related objects.</param>
      <param name="relationshipQualifier">The qualifier required to be present on the relationships.</param>
      <param name="relatedRole">The role that the related objects are required to play in the relationship.</param>
      <param name="thisRole">The role that the source class is required to play in the relationship.</param>
    </member>
    <member name="M:System.Management.RelatedObjectQuery.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.RelatedObjectQuery" /> class. If the specified string can be successfully parsed as a WQL query, it is considered to be the query string; otherwise, it is assumed to be the path of the source object for the query. In this case, the query is assumed to be an instance query.</summary>
      <param name="queryOrSourceObject">The query string or the path of the source object.</param>
    </member>
    <member name="M:System.Management.RelatedObjectQuery.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.RelatedObjectQuery" /> class for the given source object and related class. The query is assumed to be an instance query (as opposed to a schema query).</summary>
      <param name="sourceObject">The path of the source object for this query.</param>
      <param name="relatedClass">The related objects' class.</param>
    </member>
    <member name="M:System.Management.RelatedObjectQuery.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.RelatedObjectQuery" /> class for the given set of parameters. The query is assumed to be an instance query (as opposed to a schema query).</summary>
      <param name="sourceObject">The path of the source object.</param>
      <param name="relatedClass">The related objects' required class.</param>
      <param name="relationshipClass">The relationship type.</param>
      <param name="relatedQualifier">The qualifier required to be present on the related objects.</param>
      <param name="relationshipQualifier">The qualifier required to be present on the relationships.</param>
      <param name="relatedRole">The role that the related objects are required to play in the relationship.</param>
      <param name="thisRole">The role that the source object is required to play in the relationship.</param>
      <param name="classDefinitionsOnly">
        <see langword="true" /> to return only the class definitions of the related objects; otherwise, false .</param>
    </member>
    <member name="M:System.Management.RelatedObjectQuery.BuildQuery">
      <summary>Builds the query string according to the current property values.</summary>
    </member>
    <member name="P:System.Management.RelatedObjectQuery.ClassDefinitionsOnly">
      <summary>Gets or sets a value indicating that for all instances that adhere to the query, only their class definitions be returned. This parameter is only valid for instance queries.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating that for all instances that adhere to the query, only their class definitions are to be returned.</returns>
    </member>
    <member name="M:System.Management.RelatedObjectQuery.Clone">
      <summary>Creates a copy of the object.</summary>
      <returns>The copied object.</returns>
    </member>
    <member name="P:System.Management.RelatedObjectQuery.IsSchemaQuery">
      <summary>Gets or sets a value indicating whether this is a schema query or an instance query.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether this is a schema query.</returns>
    </member>
    <member name="M:System.Management.RelatedObjectQuery.ParseQuery(System.String)">
      <summary>Parses the query string and sets the property values accordingly.</summary>
      <param name="query">The query string to be parsed.</param>
    </member>
    <member name="P:System.Management.RelatedObjectQuery.RelatedClass">
      <summary>Gets or sets the class of the endpoint objects (the related class).</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the related class name.</returns>
    </member>
    <member name="P:System.Management.RelatedObjectQuery.RelatedQualifier">
      <summary>Gets or sets a qualifier required to be defined on the related objects.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the name of the qualifier required on the related object.</returns>
    </member>
    <member name="P:System.Management.RelatedObjectQuery.RelatedRole">
      <summary>Gets or sets the role that the related objects returned should be playing in the relationship.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the role of the related objects.</returns>
    </member>
    <member name="P:System.Management.RelatedObjectQuery.RelationshipClass">
      <summary>Gets or sets the type of relationship (association).</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the relationship class name.</returns>
    </member>
    <member name="P:System.Management.RelatedObjectQuery.RelationshipQualifier">
      <summary>Gets or sets a qualifier required to be defined on the relationship objects.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the name of the qualifier required on the relationship objects.</returns>
    </member>
    <member name="P:System.Management.RelatedObjectQuery.SourceObject">
      <summary>Gets or sets the source object to be used for the query. For instance queries, this is typically an instance path. For schema queries, this is typically a class name.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the path of the object to be used for the query.</returns>
    </member>
    <member name="P:System.Management.RelatedObjectQuery.ThisRole">
      <summary>Gets or sets the role that the source object should be playing in the relationship.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the role of this object.</returns>
    </member>
    <member name="T:System.Management.RelationshipQuery">
      <summary>Represents a WQL REFERENCES OF data query.</summary>
    </member>
    <member name="M:System.Management.RelationshipQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.RelationshipQuery" /> class. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.RelationshipQuery.#ctor(System.Boolean,System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.RelationshipQuery" /> class for a schema query using the given set of parameters. This constructor is used for schema queries only, so the first parameter must be true.</summary>
      <param name="isSchemaQuery">
        <see langword="true" /> to indicate that this is a schema query; otherwise, <see langword="false" />.</param>
      <param name="sourceObject">The path of the source class for this query.</param>
      <param name="relationshipClass">The type of relationship for which to query.</param>
      <param name="relationshipQualifier">A qualifier required to be present on the relationship class.</param>
      <param name="thisRole">The role that the source class is required to play in the relationship.</param>
    </member>
    <member name="M:System.Management.RelationshipQuery.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.RelationshipQuery" /> class. If the specified string can be successfully parsed as a WQL query, it is considered to be the query string; otherwise, it is assumed to be the path of the source object for the query. In this case, the query is assumed to be an instances query.</summary>
      <param name="queryOrSourceObject">The query string or the class name for this query.</param>
    </member>
    <member name="M:System.Management.RelationshipQuery.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.RelationshipQuery" /> class for the given source object and relationship class. The query is assumed to be an instance query (as opposed to a schema query).</summary>
      <param name="sourceObject">The path of the source object for this query.</param>
      <param name="relationshipClass">The type of relationship for which to query.</param>
    </member>
    <member name="M:System.Management.RelationshipQuery.#ctor(System.String,System.String,System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.RelationshipQuery" /> class for the given set of parameters. The query is assumed to be an instance query (as opposed to a schema query).</summary>
      <param name="sourceObject">The path of the source object for this query.</param>
      <param name="relationshipClass">The type of relationship for which to query.</param>
      <param name="relationshipQualifier">A qualifier required to be present on the relationship object.</param>
      <param name="thisRole">The role that the source object is required to play in the relationship.</param>
      <param name="classDefinitionsOnly">When this method returns, it contains a Boolean that indicates that only class definitions for the resulting objects are returned.</param>
    </member>
    <member name="M:System.Management.RelationshipQuery.BuildQuery">
      <summary>Builds the query string according to the current property values.</summary>
    </member>
    <member name="P:System.Management.RelationshipQuery.ClassDefinitionsOnly">
      <summary>Gets or sets a value indicating that only the class definitions of the relevant relationship objects be returned.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating that only the class definitions of the relevant relationship objects be returned.</returns>
    </member>
    <member name="M:System.Management.RelationshipQuery.Clone">
      <summary>Creates a copy of the object.</summary>
      <returns>The copied object.</returns>
    </member>
    <member name="P:System.Management.RelationshipQuery.IsSchemaQuery">
      <summary>Gets or sets a value indicating whether this query is a schema query or an instance query.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether this query is a schema query.</returns>
    </member>
    <member name="M:System.Management.RelationshipQuery.ParseQuery(System.String)">
      <summary>Parses the query string and sets the property values accordingly.</summary>
      <param name="query">The query string to be parsed.</param>
    </member>
    <member name="P:System.Management.RelationshipQuery.RelationshipClass">
      <summary>Gets or sets the class of the relationship objects wanted in the query.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the relationship class name.</returns>
    </member>
    <member name="P:System.Management.RelationshipQuery.RelationshipQualifier">
      <summary>Gets or sets a qualifier required on the relationship objects.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the name of the qualifier required on the relationship objects.</returns>
    </member>
    <member name="P:System.Management.RelationshipQuery.SourceObject">
      <summary>Gets or sets the source object for this query.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the path of the object to be used for the query.</returns>
    </member>
    <member name="P:System.Management.RelationshipQuery.ThisRole">
      <summary>Gets or sets the role of the source object in the relationship.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the role of this object.</returns>
    </member>
    <member name="T:System.Management.SelectQuery">
      <summary>Represents a WQL SELECT data query.</summary>
    </member>
    <member name="M:System.Management.SelectQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.SelectQuery" /> class. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.SelectQuery.#ctor(System.Boolean,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.SelectQuery" /> class for a schema query, optionally specifying a condition.</summary>
      <param name="isSchemaQuery">
        <see langword="true" /> to indicate that this is a schema query; otherwise, <see langword="false" />. A <see langword="false" /> value is invalid in this constructor.</param>
      <param name="condition">The condition to be applied to form the result set of classes.</param>
    </member>
    <member name="M:System.Management.SelectQuery.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.SelectQuery" /> class for the specified query or the specified class name.</summary>
      <param name="queryOrClassName">The entire query or the class name to use in the query. The parser in this class attempts to parse the string as a valid WQL SELECT query. If the parser is unsuccessful, it assumes the string is a class name.</param>
    </member>
    <member name="M:System.Management.SelectQuery.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.SelectQuery" /> class with the specified class name and condition.</summary>
      <param name="className">The name of the class to select in the query.</param>
      <param name="condition">The condition to be applied in the query.</param>
    </member>
    <member name="M:System.Management.SelectQuery.#ctor(System.String,System.String,System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Management.SelectQuery" /> class with the specified class name and condition, selecting only the specified properties.</summary>
      <param name="className">The name of the class from which to select.</param>
      <param name="condition">The condition to be applied to instances of the selected class.</param>
      <param name="selectedProperties">An array of property names to be returned in the query results.</param>
    </member>
    <member name="M:System.Management.SelectQuery.BuildQuery">
      <summary>Builds the query string according to the current property values.</summary>
    </member>
    <member name="P:System.Management.SelectQuery.ClassName">
      <summary>Gets or sets the class name to be selected from in the query.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the name of the class in the query.</returns>
    </member>
    <member name="M:System.Management.SelectQuery.Clone">
      <summary>Creates a copy of the object.</summary>
      <returns>The copied object.</returns>
    </member>
    <member name="P:System.Management.SelectQuery.Condition">
      <summary>Gets or sets the condition to be applied in the SELECT query.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the condition to be applied to the SELECT query.</returns>
    </member>
    <member name="P:System.Management.SelectQuery.IsSchemaQuery">
      <summary>Gets or sets a value indicating whether this query is a schema query or an instances query.</summary>
      <returns>Returns a <see cref="T:System.Boolean" /> value indicating whether the query is a schema query.</returns>
    </member>
    <member name="M:System.Management.SelectQuery.ParseQuery(System.String)">
      <summary>Parses the query string and sets the property values accordingly.</summary>
      <param name="query">The query string to be parsed.</param>
    </member>
    <member name="P:System.Management.SelectQuery.QueryString">
      <summary>Gets or sets the query in the <see cref="T:System.Management.SelectQuery" /> object, in string form.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the query.</returns>
    </member>
    <member name="P:System.Management.SelectQuery.SelectedProperties">
      <summary>Ggets or sets an array of property names to be selected in the query.</summary>
      <returns>Returns a <see cref="T:System.Collections.Specialized.StringCollection" /> containing the names of the properties to be selected in the query.</returns>
    </member>
    <member name="T:System.Management.StoppedEventArgs">
      <summary>Holds event data for the <see cref="E:System.Management.ManagementEventWatcher.Stopped" /> event.</summary>
    </member>
    <member name="P:System.Management.StoppedEventArgs.Status">
      <summary>Gets the completion status of the operation.</summary>
      <returns>The status of the operation.</returns>
    </member>
    <member name="T:System.Management.StoppedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:System.Management.ManagementEventWatcher.Stopped" /> event.</summary>
      <param name="sender">The instance of the object for which to invoke this method.</param>
      <param name="e">The <see cref="T:System.Management.StoppedEventArgs" /> that specifies the reason the event was invoked.</param>
    </member>
    <member name="T:System.Management.TextFormat">
      <summary>Describes the possible text formats that can be used with <see cref="M:System.Management.ManagementBaseObject.GetText(System.Management.TextFormat)" />.</summary>
    </member>
    <member name="F:System.Management.TextFormat.CimDtd20">
      <summary>XML DTD that corresponds to CIM DTD version 2.0.</summary>
    </member>
    <member name="F:System.Management.TextFormat.Mof">
      <summary>Managed Object Format</summary>
    </member>
    <member name="F:System.Management.TextFormat.WmiDtd20">
      <summary>XML WMI DTD that corresponds to CIM DTD version 2.0. Using this value enables a few WMI-specific extensions, like embedded objects.</summary>
    </member>
    <member name="T:System.Management.WqlEventQuery">
      <summary>Represents a WMI event query in WQL format.</summary>
    </member>
    <member name="M:System.Management.WqlEventQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.WqlEventQuery" /> class. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.WqlEventQuery.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.WqlEventQuery" /> class based on the given query string or event class name.</summary>
      <param name="queryOrEventClassName">The string representing either the entire event query or the name of the event class to query. The object will try to parse the string as a valid event query. If unsuccessful, the parser will assume that the parameter represents an event class name.</param>
    </member>
    <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.WqlEventQuery" /> class for the specified event class name, with the specified condition.</summary>
      <param name="eventClassName">The name of the event class to query.</param>
      <param name="condition">The condition to apply to events of the specified class.</param>
    </member>
    <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.String,System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.WqlEventQuery" /> class with the specified event class name, condition, and grouping interval.</summary>
      <param name="eventClassName">The name of the event class to query.</param>
      <param name="condition">The condition to apply to events of the specified class.</param>
      <param name="groupWithinInterval">The specified interval at which WMI sends one <c>aggregate event</c>, rather than many events.</param>
    </member>
    <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.String,System.TimeSpan,System.String[])">
      <summary>Initializes a new instance of the <see cref="T:System.Management.WqlEventQuery" /> class with the specified event class name, condition, grouping interval, and grouping properties.</summary>
      <param name="eventClassName">The name of the event class to query.</param>
      <param name="condition">The condition to apply to events of the specified class.</param>
      <param name="groupWithinInterval">The specified interval at which WMI sends one <c>aggregate event</c>, rather than many events.</param>
      <param name="groupByPropertyList">The properties in the event class by which the events should be grouped.</param>
    </member>
    <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.TimeSpan)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.WqlEventQuery" /> class for the specified event class, with the specified latency time.</summary>
      <param name="eventClassName">The name of the event class to query.</param>
      <param name="withinInterval">A <see cref="T:System.TimeSpan" /> value specifying the latency acceptable for receiving this event. This value is used in cases where there is no explicit event provider for the query requested, and WMI is required to poll for the condition. This interval is the maximum amount of time that can pass before notification of an event must be delivered.</param>
    </member>
    <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.TimeSpan,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.WqlEventQuery" /> class with the specified event class name, polling interval, and condition.</summary>
      <param name="eventClassName">The name of the event class to query.</param>
      <param name="withinInterval">A <see cref="T:System.TimeSpan" /> value specifying the latency acceptable for receiving this event. This value is used in cases where there is no explicit event provider for the query requested and WMI is required to poll for the condition. This interval is the maximum amount of time that can pass before notification of an event must be delivered.</param>
      <param name="condition">The condition to apply to events of the specified class.</param>
    </member>
    <member name="M:System.Management.WqlEventQuery.#ctor(System.String,System.TimeSpan,System.String,System.TimeSpan,System.String[],System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.WqlEventQuery" /> class with the specified event class name, condition, grouping interval, grouping properties, and specified number of events.</summary>
      <param name="eventClassName">The name of the event class on which to be queried.</param>
      <param name="withinInterval">A <see cref="T:System.TimeSpan" /> value specifying the latency acceptable for receiving this event. This value is used in cases where there is no explicit event provider for the query requested, and WMI is required to poll for the condition. This interval is the maximum amount of time that can pass before notification of an event must be delivered.</param>
      <param name="condition">The condition to apply to events of the specified class.</param>
      <param name="groupWithinInterval">The specified interval at which WMI sends one <c>aggregate event</c>, rather than many events.</param>
      <param name="groupByPropertyList">The properties in the event class by which the events should be grouped.</param>
      <param name="havingCondition">The condition to apply to the number of events.</param>
    </member>
    <member name="M:System.Management.WqlEventQuery.BuildQuery">
      <summary>Builds the query string according to the current property values.</summary>
    </member>
    <member name="M:System.Management.WqlEventQuery.Clone">
      <summary>Creates a copy of the object.</summary>
      <returns>The copied object.</returns>
    </member>
    <member name="P:System.Management.WqlEventQuery.Condition">
      <summary>Gets or sets the condition to be applied to events of the specified class.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the condition or conditions in the event query.</returns>
    </member>
    <member name="P:System.Management.WqlEventQuery.EventClassName">
      <summary>Gets or sets the event class to query.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the name of the event class in the event query.</returns>
    </member>
    <member name="P:System.Management.WqlEventQuery.GroupByPropertyList">
      <summary>Gets or sets properties in the event to be used for grouping events of the same type.</summary>
      <returns>Returns a <see cref="T:System.Collections.Specialized.StringCollection" /> containing the properties in the event to be used for grouping events of the same type.</returns>
    </member>
    <member name="P:System.Management.WqlEventQuery.GroupWithinInterval">
      <summary>Gets or sets the interval to be used for grouping events of the same type.</summary>
      <returns>Returns a <see cref="T:System.TimeSpan" /> value containing the interval used for grouping events of the same type.</returns>
    </member>
    <member name="P:System.Management.WqlEventQuery.HavingCondition">
      <summary>Gets or sets the condition to be applied to the aggregation of events, based on the number of events received.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the condition applied to the aggregation of events, based on the number of events received.</returns>
    </member>
    <member name="M:System.Management.WqlEventQuery.ParseQuery(System.String)">
      <summary>Parses the query string and sets the property values accordingly.</summary>
      <param name="query">The query string to be parsed.</param>
    </member>
    <member name="P:System.Management.WqlEventQuery.QueryLanguage">
      <summary>Gets  the language of the query.</summary>
      <returns>Returns a <see cref="T:System.String" /> value that contains the query language that the query is written in.</returns>
    </member>
    <member name="P:System.Management.WqlEventQuery.QueryString">
      <summary>Gets or sets the string representing the query.</summary>
      <returns>Returns a <see cref="T:System.String" /> value containing the query.</returns>
    </member>
    <member name="P:System.Management.WqlEventQuery.WithinInterval">
      <summary>Gets or sets the polling interval to be used in this query.</summary>
      <returns>Returns a <see cref="T:System.TimeSpan" /> value containing the polling interval used in the event query.</returns>
    </member>
    <member name="T:System.Management.WqlObjectQuery">
      <summary>Represents a WMI data query in WQL format.</summary>
    </member>
    <member name="M:System.Management.WqlObjectQuery.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Management.WqlObjectQuery" /> class. This is the parameterless constructor.</summary>
    </member>
    <member name="M:System.Management.WqlObjectQuery.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Management.WqlObjectQuery" /> class initialized to the specified query.</summary>
      <param name="query">The representation of the data query.</param>
    </member>
    <member name="M:System.Management.WqlObjectQuery.Clone">
      <summary>Creates a copy of the object.</summary>
      <returns>The copied object.</returns>
    </member>
    <member name="P:System.Management.WqlObjectQuery.QueryLanguage">
      <summary>Gets the language of the query.</summary>
      <returns>The language of the query.</returns>
    </member>
  </members>
</doc>