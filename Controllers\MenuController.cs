﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DesktopRpaLib.Actions;
using System.Web.Http;
using DesktopRpaLib.Response;



[RoutePrefix("api/menu/{ProcessId}")] // 定义路由前缀
public class MenuController : ApiController
{

    private readonly MenuAction _menuAction = new MenuAction();



    // 获取顶层菜单名称
    [Route("getTopMenus")]
    public IHttpActionResult GetTopMenus(int ProcessId)
    {
        try
        {
            var topMenus = _menuAction.GetTopMenus(ProcessId);
            var data = new { ProcessId, TopMenus = topMenus };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }

    }




    // 选择菜单
    [Route("selectMenu")]
    public IHttpActionResult PostSelectMenu(int ProcessId, [FromUri] string menuName)
    {

        if (string.IsNullOrEmpty(menuName))
        {
            return Json(ResponseResult<string>.Error(msg: "menuName参数不能为空"));
        }


        try
        {
            _menuAction.SelectMenu(ProcessId, menuName);
            var data = new { ProcessId,  MenuName = menuName };
            return Json(ResponseResult<object>.Success(msg: "点击菜单成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }

    }






    // 根据 automationId 选择子菜单
    [Route("selectMenuByAutomationId/{AutomationId}")]
    public IHttpActionResult PostSelectMenuByAutomationId(int ProcessId, string AutomationId)
    {
        try
        {
            _menuAction.SelectMenuByAutomationId(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId = AutomationId };
            return Json(ResponseResult<object>.Success(msg: "点击菜单成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }

    }



}

