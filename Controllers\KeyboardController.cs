﻿using System.Web.Http;
using DesktopRpaLib.Actions;
using DesktopRpaLib.Response;
using System;

namespace DesktopRpaLib.Controllers
{
    [RoutePrefix("api/keyboard")]
    public class KeyboardController : ApiController
    {
        private readonly KeyBoardAction _keyBoardAction = new KeyBoardAction();


        // 模拟按键
        [Route("pressKey/{key}")]
        [HttpPost]
        public IHttpActionResult PostPressKey(string key)
        {
            if (string.IsNullOrEmpty(key))
            {
                return Json(ResponseResult<string>.Error(msg: "key参数不能为空"));
            }

            try
            {
                _keyBoardAction.PressKey(key);
                return Json(ResponseResult<object>.Success(msg: $"按键 {key} 模拟成功"));
            }
            catch (Exception ex)
            {
                return Json(ResponseResult<string>.Error(msg: ex.Message));
            }
        }



    }
}
