﻿using Owin; // 导入Owin命名空间，用于自托管OWIN应用程序
using System.Web.Http; // 导入System.Web.Http命名空间，用于Web API相关的功能
using Microsoft.Owin.Cors; // 导入CORS中间件命名空间

public class Startup
{
    public void Configuration(IAppBuilder appBuilder) // 定义Configuration方法，用于配置OWIN管道
    {
        // 创建一个新的HttpConfiguration对象，用于配置Web API
        HttpConfiguration config = new HttpConfiguration();

        // 启用属性路由
        config.MapHttpAttributeRoutes();

        // 启用 CORS
        appBuilder.UseCors(CorsOptions.AllowAll);

        // 添加自定义日志处理器
        config.MessageHandlers.Add(new LoggingHandler());

        // 使用UseWebApi方法将配置好的Web API添加到OWIN管道中
        appBuilder.UseWebApi(config);
    }
}
