﻿using System.Web.Http;
using DesktopRpaLib.Actions;
using DesktopRpaLib.Response;
using System;
using System.Collections.Generic;




[RoutePrefix("api/tab/{ProcessId}/{tabParentAutomationId}")] // 定义路由前缀
public class TabController : ApiController
{
    private readonly TabAction _tabAction = new TabAction();


    // 选择指定的选项卡项目
    [Route("select")]
    public IHttpActionResult PostSelectTabItemByName(int ProcessId, string tabParentAutomationId, [FromUri] string tabName)
    {

        if (string.IsNullOrEmpty(tabName))
        {
            return Json(ResponseResult<string>.Error(msg: "tabName参数不能为空"));
        }


        try
        {
            _tabAction.SelectTabItemByName(ProcessId, tabParentAutomationId, tabName);
            return Json(ResponseResult<object>.Success(msg: "选项卡项目已选择"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 获取所有选项卡项目的名称列表
    //[Route("getAllTabNames")]
    //public IHttpActionResult GetAllTabNames(int ProcessId, string tabParentAutomationId)
    //{
    //    try
    //    {
    //        List<string> tabNames = _tabAction.GetAllTabNames(ProcessId, tabParentAutomationId);
    //        return Json(ResponseResult<object>.Success(msg: "查询成功", data: tabNames));
    //    }
    //    catch (Exception ex)
    //    {
    //        return Json(ResponseResult<string>.Error(msg: ex.Message));
    //    }
    //}




}