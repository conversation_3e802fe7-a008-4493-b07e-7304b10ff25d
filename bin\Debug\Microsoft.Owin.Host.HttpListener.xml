<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Owin.Host.HttpListener</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Owin.Host.HttpListener.OwinHttpListener">
            <summary>
            This wraps HttpListener and exposes it as an OWIN compatible server.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Host.HttpListener.OwinHttpListener.#ctor">
            <summary>
            Creates a listener wrapper that can be configured by the user before starting.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Host.HttpListener.OwinHttpListener.Listener">
            <summary>
            The HttpListener instance wrapped by this wrapper.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Host.HttpListener.OwinHttpListener.SetRequestProcessingLimits(System.Int32,System.Int32)">
            <summary>
            These are merged as one call because they should be swapped out atomically.
            This controls how many requests the server attempts to process concurrently.
            </summary>
            <param name="maxAccepts">The maximum number of pending request receives.</param>
            <param name="maxRequests">The maximum number of active requests being processed.</param>
        </member>
        <member name="M:Microsoft.Owin.Host.HttpListener.OwinHttpListener.GetRequestProcessingLimits(System.Int32@,System.Int32@)">
            <summary>
            
            </summary>
            <param name="maxAccepts"></param>
            <param name="maxRequests"></param>
        </member>
        <member name="M:Microsoft.Owin.Host.HttpListener.OwinHttpListener.SetRequestQueueLimit(System.Int64)">
            <summary>
            Sets the maximum number of requests that will be queued up in Http.Sys.
            </summary>
            <param name="limit"></param>
        </member>
        <member name="M:Microsoft.Owin.Host.HttpListener.OwinHttpListener.Start(System.Net.HttpListener,System.Func{System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.Tasks.Task},System.Collections.Generic.IList{System.Collections.Generic.IDictionary{System.String,System.Object}},System.Collections.Generic.IDictionary{System.String,System.Object},System.Func{System.String,System.Func{System.Diagnostics.TraceEventType,System.Int32,System.Object,System.Exception,System.Func{System.Object,System.Exception,System.String},System.Boolean}})">
            <summary>
            Starts the listener and request processing threads.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Host.HttpListener.OwinHttpListener.Dispose">
            <summary>
            Shuts down the listener and disposes it.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Host.HttpListener.OwinServerFactory">
            <summary>
            Implements the Katana setup pattern for the OwinHttpListener server.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Host.HttpListener.OwinServerFactory.Initialize(System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Advertise the capabilities of the server.
            </summary>
            <param name="properties"></param>
        </member>
        <member name="M:Microsoft.Owin.Host.HttpListener.OwinServerFactory.Create(System.Func{System.Collections.Generic.IDictionary{System.String,System.Object},System.Threading.Tasks.Task},System.Collections.Generic.IDictionary{System.String,System.Object})">
            <summary>
            Creates an OwinHttpListener and starts listening on the given URL.
            </summary>
            <param name="app">The application entry point.</param>
            <param name="properties">The addresses to listen on.</param>
            <returns>The OwinHttpListener.  Invoke Dispose to shut down.</returns>
        </member>
        <member name="T:Microsoft.Owin.Host.HttpListener.RequestProcessing.ExceptionFilterStream">
            <summary>
            This class is used to wrap other streams and convert some exception types.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Host.HttpListener.RequestProcessing.OwinHttpListenerRequest">
            <summary>
            This wraps an HttpListenerRequest and exposes it as an OWIN environment IDictionary.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Host.HttpListener.RequestProcessing.OwinHttpListenerRequest.#ctor(System.Net.HttpListenerRequest,System.String,System.String,System.String,Microsoft.Owin.Host.HttpListener.RequestProcessing.CallEnvironment)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Host.HttpListener.RequestProcessing.OwinHttpListenerRequest"/> class.
            Uses the given request object to populate the OWIN standard keys in the environment IDictionary.
            Most values are copied so that they can be mutable, but the headers collection is only wrapped.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Host.HttpListener.RequestProcessing.OwinHttpListenerResponse">
            <summary>
            This wraps an HttpListenerResponse, populates it with the given response fields, and relays 
            the response body to the underlying stream.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Host.HttpListener.RequestProcessing.OwinHttpListenerResponse.#ctor(System.Net.HttpListenerContext,Microsoft.Owin.Host.HttpListener.RequestProcessing.CallEnvironment)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Owin.Host.HttpListener.RequestProcessing.OwinHttpListenerResponse"/> class.
            Sets up the Environment with the necessary request state items.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Host.HttpListener.RequestProcessing.RequestHeadersDictionary">
            <summary>
            This wraps HttpListenerRequest's WebHeaderCollection (NameValueCollection) and adapts it to 
            the OWIN required IDictionary surface area. It remains fully mutable, but you will be subject 
            to the header validations performed by the underlying collection.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Host.HttpListener.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Host.HttpListener.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Host.HttpListener.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Host.HttpListener.Resources.Exception_DuplicateKey">
            <summary>
              Looks up a localized string similar to The key &apos;{0}&apos; is already present in the dictionary..
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Host.HttpListener.Resources.Exception_ResponseAlreadySent">
            <summary>
              Looks up a localized string similar to The response headers have already been sent..
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Host.HttpListener.WebSockets.Constants">
            <summary>
            Standard keys and values for use within the OWIN interfaces
            </summary>
        </member>
    </members>
</doc>
