# 桌面RPA API PowerShell测试脚本
# 使用方法: .\test_api.ps1

$API_BASE = "http://localhost:9000"

# 通用请求函数
function Invoke-ApiRequest {
    param(
        [string]$Url,
        [string]$Method = "GET",
        [string]$TestName
    )
    
    Write-Host "`n$('='*50)" -ForegroundColor Yellow
    Write-Host "测试: $TestName" -ForegroundColor Yellow
    Write-Host "$('='*50)" -ForegroundColor Yellow
    
    try {
        $response = Invoke-RestMethod -Uri $Url -Method $Method -TimeoutSec 10
        Write-Host "请求成功!" -ForegroundColor Green
        Write-Host ($response | ConvertTo-Json -Depth 3) -ForegroundColor White
        return $response
    }
    catch {
        Write-Host "请求失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 测试基础API
function Test-BasicApis {
    Write-Host "`n开始测试基础API..." -ForegroundColor Cyan
    
    # 健康检查
    $result = Invoke-ApiRequest -Url "$API_BASE/api/hello" -TestName "健康检查"
    if (-not $result) {
        Write-Host "API服务可能未启动，请检查 http://localhost:9000" -ForegroundColor Red
        return $false
    }
    
    # 获取串口列表
    Invoke-ApiRequest -Url "$API_BASE/api/serialPort/getPortNames" -TestName "获取串口列表"
    
    # 获取可移动驱动器
    Invoke-ApiRequest -Url "$API_BASE/api/disk/getRemovableDrives" -TestName "获取可移动驱动器"
    
    return $true
}

# 测试键盘API
function Test-KeyboardApi {
    Write-Host "`n开始测试键盘API..." -ForegroundColor Cyan
    
    $keys = @("enter", "tab", "up", "down")
    foreach ($key in $keys) {
        Invoke-ApiRequest -Url "$API_BASE/api/keyboard/pressKey/$key" -Method "POST" -TestName "模拟按键 $key"
        Start-Sleep -Milliseconds 500
    }
}

# 测试进程API
function Test-ProcessApi {
    Write-Host "`n开始测试进程API..." -ForegroundColor Cyan
    
    $processNames = @("notepad", "explorer", "winlogon")
    
    foreach ($processName in $processNames) {
        $result = Invoke-ApiRequest -Url "$API_BASE/api/exe/getPidsByExeName?exeName=$processName" -TestName "获取 $processName 进程PID"
        
        # 如果成功获取到PID，尝试获取进程信息
        if ($result -and $result.success -and $result.data.AllPids) {
            $pid = $result.data.AllPids[0]
            Invoke-ApiRequest -Url "$API_BASE/api/exe/getExeNameAndTitle/$pid" -TestName "获取进程 $pid 信息"
            break
        }
    }
}

# 测试窗口API
function Test-WindowApi {
    Write-Host "`n开始测试窗口API..." -ForegroundColor Cyan
    
    $windowTitles = @("Program Manager", "桌面", "Desktop")
    
    foreach ($title in $windowTitles) {
        $encodedTitle = [System.Web.HttpUtility]::UrlEncode($title)
        Invoke-ApiRequest -Url "$API_BASE/api/window/windowExistsByTitle?title=$encodedTitle" -TestName "检查窗口 '$title' 是否存在"
    }
}

# 测试元素API
function Test-ElementApi {
    Write-Host "`n开始测试元素API..." -ForegroundColor Cyan
    
    # 首先尝试获取explorer进程ID
    $result = Invoke-ApiRequest -Url "$API_BASE/api/exe/getPidsByExeName?exeName=explorer" -TestName "获取Explorer进程ID"
    
    if ($result -and $result.success -and $result.data.AllPids) {
        $pid = $result.data.AllPids[0]
        
        $elementNames = @("确定", "取消", "OK", "Cancel")
        foreach ($elementName in $elementNames) {
            $encodedName = [System.Web.HttpUtility]::UrlEncode($elementName)
            Invoke-ApiRequest -Url "$API_BASE/api/element/$pid/isExistByName?elementName=$encodedName" -TestName "检查元素 '$elementName' 是否存在"
        }
    }
}

# 运行综合测试
function Start-ComprehensiveTest {
    Write-Host "桌面RPA API综合测试开始..." -ForegroundColor Green
    Write-Host "API服务地址: $API_BASE" -ForegroundColor Green
    
    # 加载System.Web程序集用于URL编码
    Add-Type -AssemblyName System.Web
    
    # 测试API服务是否可用
    if (-not (Test-BasicApis)) {
        Write-Host "`n基础API测试失败，请检查服务是否启动" -ForegroundColor Red
        return
    }
    
    # 运行各项测试
    Test-KeyboardApi
    Test-ProcessApi
    Test-WindowApi
    Test-ElementApi
    
    Write-Host "`n$('='*50)" -ForegroundColor Green
    Write-Host "测试完成！" -ForegroundColor Green
    Write-Host "$('='*50)" -ForegroundColor Green
}

# 交互式测试菜单
function Show-InteractiveMenu {
    while ($true) {
        Write-Host "`n$('='*50)" -ForegroundColor Yellow
        Write-Host "桌面RPA API交互式测试" -ForegroundColor Yellow
        Write-Host "$('='*50)" -ForegroundColor Yellow
        Write-Host "1. 基础API测试" -ForegroundColor White
        Write-Host "2. 键盘操作测试" -ForegroundColor White
        Write-Host "3. 进程操作测试" -ForegroundColor White
        Write-Host "4. 窗口操作测试" -ForegroundColor White
        Write-Host "5. 元素操作测试" -ForegroundColor White
        Write-Host "6. 运行全部测试" -ForegroundColor White
        Write-Host "0. 退出" -ForegroundColor White
        
        $choice = Read-Host "`n请选择测试项目 (0-6)"
        
        # 加载System.Web程序集用于URL编码
        Add-Type -AssemblyName System.Web
        
        switch ($choice) {
            "0" { 
                Write-Host "退出测试" -ForegroundColor Green
                return 
            }
            "1" { Test-BasicApis }
            "2" { Test-KeyboardApi }
            "3" { Test-ProcessApi }
            "4" { Test-WindowApi }
            "5" { Test-ElementApi }
            "6" { Start-ComprehensiveTest }
            default { 
                Write-Host "无效选择，请重新输入" -ForegroundColor Red 
            }
        }
    }
}

# 主程序入口
param(
    [switch]$Interactive
)

if ($Interactive) {
    Show-InteractiveMenu
} else {
    Start-ComprehensiveTest
}

# 使用示例:
# .\test_api.ps1                # 运行全部测试
# .\test_api.ps1 -Interactive   # 交互式测试
