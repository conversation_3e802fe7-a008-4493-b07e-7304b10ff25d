﻿using System;
using System.Diagnostics;
using System.Windows.Automation;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using FlaUI.Core;
using FlaUI.Core.AutomationElements;
using FlaUI.UIA3;
using FlaUI.Core.Definitions;
using System.Runtime.InteropServices;



namespace DesktopRpaLib.Actions
{
    public class ExeAction
    {



        // 根据 exe名称获取所有PID
        public int[] GetPidsByExeName(string exeName)
        {
            List<int> pidList = new List<int>();

            // 获取所有运行中的进程，按进程名过滤
            Process[] processes = Process.GetProcessesByName(exeName);

            if (processes.Length == 0)
            {
                throw new Exception($"没有找到名为 {exeName}.exe 的进程");
            }

            // 遍历所有进程并获取 PID
            foreach (var process in processes)
            {
                pidList.Add(process.Id);
            }

            return pidList.ToArray();
        }



        // 根据 PID 获取 exeName 和 主窗口标题
        public (string exeName, string exePath, string mainWindowTitle) GetExeInfoByPid(int processId)
        {
            Process process = Process.GetProcessById(processId);

            // 获取完整的 exe 路径
            string exePath = process.MainModule.FileName;

            // 从路径中提取文件名（例如 DiskMark64.exe）
            string exeName = Path.GetFileName(exePath);

            // 获取主窗口标题
            string mainWindowTitle = process.MainWindowTitle;

            return (exeName, exePath, mainWindowTitle);
        }




        // 杀死指定 PID 的进程
        public void KillProcessByPid(int processId)
        {
            try
            {
                // 获取进程对象
                Process process = Process.GetProcessById(processId);

                // 执行终止操作
                process.Kill();
            }
            catch (Exception ex)
            {
                throw new Exception($"无法终止进程 {processId}: {ex.Message}");
            }
        }




        // 启动指定路径的快捷方式
        public void StartShortcut(string shortcutPath)
        {
            try
            {
                // 检查快捷方式是否存在
                if (File.Exists(shortcutPath))
                {
                    Process.Start("explorer", shortcutPath);
                }
                else
                {
                    throw new Exception($"快捷方式 {shortcutPath} 不存在");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"启动快捷方式失败: {ex.Message}");
            }
        }






        // 启动指定路径的 exe
        public int StartExe(string exePath)
        {
            try
            {
                // 检查文件是否存在
                if (File.Exists(exePath))
                {
                    Process process = Process.Start(exePath);
                    return process.Id;
                }
                else
                {
                    throw new Exception($"exe文件 {exePath} 不存在");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"启动 exe 失败: {ex.Message}");
            }
        }



        // 使用管理员身份启动指定路径的 exe
        public int StartExeByAdmin(string exePath)
        {
            try
            {
                // 检查文件是否存在
                if (File.Exists(exePath))
                {
                    ProcessStartInfo startInfo = new ProcessStartInfo
                    {
                        FileName = exePath,
                        Verb = "runas"  // 设置为 "runas" 来以管理员身份运行
                    };

                    Process process = Process.Start(exePath);
                    return process.Id;
                }
                else
                {
                    throw new Exception($"文件 {exePath} 不存在");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"以管理员身份启动 exe 失败: {ex.Message}");
            }
        }



        // 处理Windows 安全中心弹框
        public void DealSafeCenterPopUp(int dealTime, bool block)
        {
            try
            {
                // 启动子线程检测 "Windows 安全中心" 弹框
                Thread detectionThread = new Thread(() =>
                {
                    ExecuteTaskWithTimeout(CheckAndConfirmWindowsSecurityPrompt, dealTime * 1000, 500);
                });

                detectionThread.Start();

                // 等待线程完成（可选）
                if (block)
                {
                    detectionThread.Join();
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"处理Windows 安全中心弹框失败: {ex.Message}");
            }

        }





        public bool CheckAndConfirmWindowsSecurityPrompt()
        {
            // 启用 UIA3 自动化模式
            using (var automation = new UIA3Automation())
            {
                var window = automation.GetDesktop().FindFirstDescendant(cf => cf.ByName("Windows 安全中心"));

                if (window != null)
                {
                    Console.WriteLine("找到弹框!");

                    // 查找并点击 "是" 按钮（按钮名可能因系统语言不同而有所区别）
                    var yesButton = window.FindFirstDescendant(cf => cf.ByName("允许"));

                    if (yesButton != null)
                    {
                        // 检查按钮是否启用
                        if (yesButton.IsEnabled)
                        {
                            yesButton.AsButton().Invoke(); // 执行点击
                            Console.WriteLine("已点击 '是' 按钮");
                        }
                        else
                        {
                            Console.WriteLine("按钮不可用，跳过点击操作");
                        }
                    }
                    else
                    {
                        Console.WriteLine("'允许' 按钮未找到");
                    }

                }
                else
                {
                    Console.WriteLine("未找到弹框");
                    return false;
                }

                return false;

            }
        }





        public void ExecuteTaskWithTimeout(Func<bool> task, int timeout, int interval)
        {
            int elapsedTime = 0;

            while (elapsedTime < timeout)
            {
                if (task())
                {
                    return;
                }

                Thread.Sleep(interval);
                elapsedTime += interval;
            }
        }







    }

}
