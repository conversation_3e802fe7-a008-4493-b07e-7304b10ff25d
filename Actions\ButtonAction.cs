using System;
using System.Diagnostics;
using System.Windows.Automation;
using System.Threading;
using System.Threading.Tasks;
using System.Runtime.InteropServices;

namespace DesktopRpaLib.Actions
{
    class ButtonAction
    {


        // 消息常量：BM_CLICK 用于模拟点击按钮
        private const uint BM_CLICK = 0x00F5;


        // P/Invoke 声明：SendMessage
        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);



        // 验证按钮可用并可点击
        public InvokePattern verifyButton(AutomationElement element)
        {
            if (!(bool)element.GetCurrentPropertyValue(AutomationElement.IsEnabledProperty))
            {
                throw new Exception("按钮不可用");
            }

            var invokePattern = element.GetCurrentPattern(InvokePattern.Pattern) as InvokePattern;
            if (invokePattern == null)
            {
                throw new Exception("按钮不支持点击操作");
            }

            return invokePattern;
        }



        // 按钮是否可用
        public bool IsButtonEnabled(int processId, string automationId)
        {
            return ActionUtil.IsElementEnabled(processId, automationId);
        }




        // 获取按钮文本
        public string GetButtonText(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var buttonText = element.GetCurrentPropertyValue(AutomationElement.NameProperty)?.ToString();

            if (string.IsNullOrEmpty(buttonText))
            {
                throw new Exception("按钮文本为空或未设置");
            }

            return buttonText;
        }



        // 单击 Button
        public void ClickButton(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var invokePattern = verifyButton(element);

            invokePattern.Invoke();
        }




        // 异步单击 Button
        public async void ClickButtonAsync(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var invokePattern = verifyButton(element);

            await Task.Run(() => invokePattern.Invoke());
        }





        // 双击 Button
        public void DoubleClickButton(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var invokePattern = verifyButton(element);

            invokePattern.Invoke(); // 第一次点击
            System.Threading.Thread.Sleep(100); // 等待一小段时间模拟双击
            invokePattern.Invoke(); // 第二次点击
        }



    }
}
