# 桌面RPA API接口文档

## 基础信息
- **Base URL**: `http://localhost:9000`
- **Content-Type**: `application/json`
- **响应格式**: JSON
- **框架**: ASP.NET Web API + OWIN自托管
- **支持CORS**: 是

## 通用响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "errorCode": null
}
```

## 0. 测试接口 (Hello)

### 0.1 健康检查
- **URL**: `GET /api/hello`
- **功能**: 测试API服务是否正常运行
- **响应**:
```json
{
  "Name": "Success",
  "Message": "Hello"
}
```

## 1. 按钮操作 (Button)

### 1.1 检查按钮是否可用
- **URL**: `GET /api/button/{ProcessId}/{AutomationId}/isEnabled`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnSubmit",
    "IsEnabled": true
  }
}
```

### 1.2 获取按钮文本
- **URL**: `GET /api/button/{ProcessId}/{AutomationId}/getText`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnSubmit",
    "ButtonText": "提交"
  }
}
```

### 1.3 单击按钮
- **URL**: `POST /api/button/{ProcessId}/{AutomationId}/click`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "单击成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnSubmit"
  }
}
```

### 1.4 异步单击按钮
- **URL**: `POST /api/button/{ProcessId}/{AutomationId}/clickAsync`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "异步单击成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnSubmit"
  }
}
```

### 1.5 双击按钮
- **URL**: `POST /api/button/{ProcessId}/{AutomationId}/doubleClick`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "双击成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnSubmit"
  }
}
```

## 2. 编辑框操作 (Edit)

### 2.1 获取编辑框文本
- **URL**: `GET /api/edit/{ProcessId}/{AutomationId}/getText`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "txtInput",
    "EditText": "输入的文本内容"
  }
}
```

### 2.2 设置编辑框文本
- **URL**: `POST /api/edit/{ProcessId}/{AutomationId}/setText?text={text}`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
  - `text` (string): 要设置的文本内容
- **响应**:
```json
{
  "success": true,
  "message": "文本已设置"
}
```

### 2.3 清空编辑框文本
- **URL**: `POST /api/edit/{ProcessId}/{AutomationId}/clearText`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "文本已清空"
}
```

### 2.4 检查编辑框是否可用
- **URL**: `GET /api/edit/{ProcessId}/{AutomationId}/isEnabled`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "txtInput",
    "IsEnabled": true
  }
}
```

## 3. 复选框操作 (CheckBox)

### 3.1 获取复选框文本
- **URL**: `GET /api/checkbox/{ProcessId}/{AutomationId}/getText`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "chkAgree",
    "CheckBoxText": "我同意条款"
  }
}
```

### 3.2 检查复选框是否可用
- **URL**: `GET /api/checkbox/{ProcessId}/{AutomationId}/isEnabled`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "chkAgree",
    "IsEnabled": true
  }
}
```

### 3.3 点击复选框（切换状态）
- **URL**: `POST /api/checkbox/{ProcessId}/{AutomationId}/click`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "复选框状态已切换"
}
```

### 3.4 获取复选框勾选状态
- **URL**: `GET /api/checkbox/{ProcessId}/{AutomationId}/isChecked`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "chkAgree",
    "IsChecked": true
  }
}
```

### 3.5 勾选复选框
- **URL**: `POST /api/checkbox/{ProcessId}/{AutomationId}/check`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "复选框已勾选"
}
```

### 3.6 取消勾选复选框
- **URL**: `POST /api/checkbox/{ProcessId}/{AutomationId}/uncheck`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "复选框已取消勾选"
}
```

## 4. 下拉框操作 (ComboBox)

### 4.1 获取所有选项值
- **URL**: `GET /api/comboBox/{ProcessId}/{AutomationId}/getValues`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "cmbCity",
    "AllValues": ["北京", "上海", "广州", "深圳"]
  }
}
```

### 4.2 获取当前选中值
- **URL**: `GET /api/comboBox/{ProcessId}/{AutomationId}/getCurrentValue`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "cmbCity",
    "CurrentValue": "北京"
  }
}
```

### 4.3 选择指定值
- **URL**: `POST /api/comboBox/{ProcessId}/{AutomationId}/select?value={value}`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
  - `value` (string): 要选择的值
- **响应**:
```json
{
  "success": true,
  "message": "选择成功"
}
```

## 5. 单选按钮操作 (RadioButton)

### 5.1 获取单选按钮文本
- **URL**: `GET /api/radioButton/{ProcessId}/{AutomationId}/getText`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "rbMale",
    "RadioButtonText": "男"
  }
}
```

### 5.2 检查单选按钮是否可用
- **URL**: `GET /api/radioButton/{ProcessId}/{AutomationId}/isEnabled`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "rbMale",
    "IsEnabled": true
  }
}
```

### 5.3 点击单选按钮
- **URL**: `POST /api/radioButton/{ProcessId}/{AutomationId}/click`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "单选按钮状态已切换"
}
```

## 6. 静态文本操作 (Static)

### 6.1 获取静态文本内容
- **URL**: `GET /api/static/{ProcessId}/{AutomationId}/getText`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "lblTitle",
    "StaticText": "欢迎使用系统"
  }
}
```

## 7. 菜单操作 (Menu)

### 7.1 获取顶层菜单
- **URL**: `GET /api/menu/{ProcessId}/getTopMenus`
- **参数**:
  - `ProcessId` (int): 进程ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "TopMenus": ["文件", "编辑", "视图", "帮助"]
  }
}
```

### 7.2 选择菜单
- **URL**: `POST /api/menu/{ProcessId}/selectMenu?menuName={menuName}`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `menuName` (string): 菜单名称
- **响应**:
```json
{
  "success": true,
  "message": "点击菜单成功",
  "data": {
    "ProcessId": 1234,
    "MenuName": "文件"
  }
}
```

### 7.3 根据AutomationId选择菜单
- **URL**: `POST /api/menu/{ProcessId}/selectMenuByAutomationId/{AutomationId}`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "点击菜单成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "menuFile"
  }
}
```

## 8. 键盘操作 (Keyboard)

### 8.1 模拟按键
- **URL**: `POST /api/keyboard/pressKey/{key}`
- **参数**:
  - `key` (string): 按键名称 (支持: "up", "down", "left", "right", "tab", "enter")
- **响应**:
```json
{
  "success": true,
  "message": "按键 Enter 模拟成功"
}
```

## 9. 串口操作 (SerialPort)

### 9.1 获取所有串口号
- **URL**: `GET /api/serialPort/getPortNames`
- **响应**:
```json
{
  "success": true,
  "message": "获取所有串口号成功",
  "data": {
    "PortNames": ["COM1", "COM2", "COM3"]
  }
}
```

### 9.2 上电
- **URL**: `POST /api/serialPort/powerOn?portName={portName}&portNumber={portNumber}`
- **参数**:
  - `portName` (string): 串口名称
  - `portNumber` (int): 端口号
- **响应**:
```json
{
  "success": true,
  "message": "上电成功"
}
```

### 9.3 断电
- **URL**: `POST /api/serialPort/powerOff?portName={portName}&portNumber={portNumber}`
- **参数**:
  - `portName` (string): 串口名称
  - `portNumber` (int): 端口号
- **响应**:
```json
{
  "success": true,
  "message": "断电成功"
}
```

### 9.4 获取Hub信息
- **URL**: `GET /api/serialPort/getHubInfo`
- **响应**:
```json
{
  "success": true,
  "message": "获取成功",
  "data": {
    "HubInfo": "Hub信息内容"
  }
}
```

## 10. 磁盘操作 (Disk)

### 10.1 获取所有可移动驱动器
- **URL**: `GET /api/disk/getRemovableDrives`
- **响应**:
```json
{
  "success": true,
  "message": "查询可移动驱动器成功",
  "data": {
    "RemovableDrives": ["D:", "E:", "F:"]
  }
}
```

### 10.2 获取指定驱动器信息
- **URL**: `GET /api/disk/getVolumeInfo?driveLetter={driveLetter}`
- **参数**:
  - `driveLetter` (string): 驱动器盘符
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "VolumeInfo": "驱动器详细信息"
  }
}
```

### 10.3 快速格式化
- **URL**: `POST /api/disk/quickFormat?driveLetter={driveLetter}&fileSystem={fileSystem}&label={label}`
- **参数**:
  - `driveLetter` (string): 驱动器盘符
  - `fileSystem` (string, 可选): 文件系统类型
  - `label` (string, 可选): 卷标
- **响应**:
```json
{
  "success": true,
  "message": "D: 快速格式化成功",
  "data": {
    "DriveLetter": "D:",
    "FileSystem": "NTFS",
    "Label": "MyDisk"
  }
}
```

## 11. 元素操作 (Element)

### 11.1 根据名称检查元素是否存在
- **URL**: `GET /api/element/{ProcessId}/isExistByName?elementName={elementName}`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `elementName` (string): 元素名称
- **响应**:
```json
{
  "success": true,
  "message": "元素存在",
  "data": {
    "ProcessId": 1234,
    "ElementName": "确定",
    "Exists": true
  }
}
```

### 11.2 根据AutomationId检查元素是否存在
- **URL**: `GET /api/element/{ProcessId}/isExistByAutomationId?automationId={automationId}`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `automationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "元素存在",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnOK",
    "Exists": true
  }
}
```

## 12. 可执行文件操作 (Exe)

### 12.1 根据exe名称获取PID列表
- **URL**: `GET /api/exe/getPidsByExeName?exeName={exeName}`
- **参数**:
  - `exeName` (string): 可执行文件名称（不包含.exe后缀）
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ExeName": "notepad",
    "AllPids": [1234, 5678]
  }
}
```

### 12.2 根据PID获取exe信息和窗口标题
- **URL**: `GET /api/exe/getExeNameAndTitle/{processId}`
- **参数**:
  - `processId` (int): 进程ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ExePath": "C:\\Windows\\System32\\notepad.exe",
    "ExeName": "notepad.exe",
    "MainWindowTitle": "无标题 - 记事本"
  }
}
```

### 12.3 杀死指定PID的进程
- **URL**: `POST /api/exe/killProcessByPid/{processId}`
- **参数**:
  - `processId` (int): 进程ID
- **响应**:
```json
{
  "success": true,
  "message": "杀死PID:1234成功"
}
```

### 12.4 启动快捷方式
- **URL**: `POST /api/exe/startShortcut?shortcutPath={shortcutPath}`
- **参数**:
  - `shortcutPath` (string): 快捷方式路径
- **响应**:
```json
{
  "success": true,
  "message": "启动成功"
}
```

### 12.5 启动exe文件
- **URL**: `POST /api/exe/startExe?exePath={exePath}`
- **参数**:
  - `exePath` (string): 可执行文件路径
- **响应**:
```json
{
  "success": true,
  "message": "启动exe成功",
  "data": {
    "Pid": 1234
  }
}
```

### 12.6 以管理员身份启动exe文件
- **URL**: `POST /api/exe/startExeByAdmin?exePath={exePath}`
- **参数**:
  - `exePath` (string): 可执行文件路径
- **响应**:
```json
{
  "success": true,
  "message": "管理员身份启动exe成功",
  "data": {
    "Pid": 1234
  }
}
```

### 12.7 处理Windows安全中心弹框
- **URL**: `POST /api/exe/dealSafeCenterPopUp?dealTime={dealTime}&block={block}`
- **参数**:
  - `dealTime` (int, 可选): 等待时间（秒），默认10
  - `block` (bool, 可选): 是否阻塞，默认false
- **响应**:
```json
{
  "success": true,
  "message": "处理Windows安全中心弹框成功"
}
```

## 13. 选项卡操作 (Tab)

### 13.1 选择指定的选项卡
- **URL**: `POST /api/tab/{ProcessId}/{tabParentAutomationId}/select?tabName={tabName}`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `tabParentAutomationId` (string): 选项卡父容器的自动化ID
  - `tabName` (string): 选项卡名称
- **响应**:
```json
{
  "success": true,
  "message": "选项卡项目已选择"
}
```

## 14. 列表操作 (List)

### 14.1 选择列表项
- **URL**: `POST /api/list/{ProcessId}/{AutomationId}/select?itemName={itemName}`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
  - `itemName` (string): 列表项名称
- **响应**:
```json
{
  "success": true,
  "message": "ListViewItem已选择"
}
```

### 14.2 获取所有列表项名称
- **URL**: `GET /api/list/{ProcessId}/{AutomationId}/getItemNames`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "成功获取所有ListViewItem名称",
  "data": ["项目1", "项目2", "项目3"]
}
```

## 15. 窗口操作 (Window)

### 15.1 获取状态栏文本
- **URL**: `GET /api/window/getStatusBarText/{processId}`
- **参数**:
  - `processId` (int): 进程ID
- **响应**:
```json
{
  "success": true,
  "message": "获取状态栏文本成功",
  "data": {
    "Text": "就绪"
  }
}
```

### 15.2 设置窗口大小
- **URL**: `POST /api/window/setSize/{processId}/{width}/{height}`
- **参数**:
  - `processId` (int): 进程ID
  - `width` (int): 窗口宽度
  - `height` (int): 窗口高度
- **响应**:
```json
{
  "success": true,
  "message": "窗口大小设置成功"
}
```

### 15.3 窗口恢复
- **URL**: `POST /api/window/restore/{processId}`
- **参数**:
  - `processId` (int): 进程ID
- **响应**:
```json
{
  "success": true,
  "message": "窗口恢复成功"
}
```

### 15.4 窗口最小化
- **URL**: `POST /api/window/minimize/{processId}`
- **参数**:
  - `processId` (int): 进程ID
- **响应**:
```json
{
  "success": true,
  "message": "窗口最小化成功"
}
```

### 15.5 窗口最大化
- **URL**: `POST /api/window/maximize/{processId}`
- **参数**:
  - `processId` (int): 进程ID
- **响应**:
```json
{
  "success": true,
  "message": "窗口最大化成功"
}
```

### 15.6 窗口置顶
- **URL**: `POST /api/window/top/{processId}`
- **参数**:
  - `processId` (int): 进程ID
- **响应**:
```json
{
  "success": true,
  "message": "窗口置顶成功"
}
```

### 15.7 窗口获取焦点
- **URL**: `POST /api/window/focus/{processId}`
- **参数**:
  - `processId` (int): 进程ID
- **响应**:
```json
{
  "success": true,
  "message": "窗口获取焦点成功"
}
```

### 15.8 移动窗口
- **URL**: `POST /api/window/move/{processId}/{x}/{y}`
- **参数**:
  - `processId` (int): 进程ID
  - `x` (int): X坐标
  - `y` (int): Y坐标
- **响应**:
```json
{
  "success": true,
  "message": "窗口移动成功"
}
```

### 15.9 获取窗口大小和位置
- **URL**: `GET /api/window/sizePos/{processId}`
- **参数**:
  - `processId` (int): 进程ID
- **响应**:
```json
{
  "success": true,
  "message": "获取窗口大小和坐标成功",
  "data": {
    "Width": 800,
    "Height": 600,
    "X": 100,
    "Y": 100
  }
}
```

### 15.10 获取对话框大小和位置
- **URL**: `GET /api/window/dialogSizePos/{processId}?dialogName={dialogName}`
- **参数**:
  - `processId` (int): 进程ID
  - `dialogName` (string): 对话框名称
- **响应**:
```json
{
  "success": true,
  "message": "获取对话框大小和坐标成功",
  "data": {
    "Width": 400,
    "Height": 300,
    "X": 200,
    "Y": 150
  }
}
```

### 15.11 点击指定坐标
- **URL**: `POST /api/window/click/{processId}/{x}/{y}`
- **参数**:
  - `processId` (int): 进程ID
  - `x` (int): X坐标
  - `y` (int): Y坐标
- **响应**:
```json
{
  "success": true,
  "message": "点击成功"
}
```

### 15.12 关闭对话框
- **URL**: `POST /api/window/closeDialog/{processId}?dialogName={dialogName}`
- **参数**:
  - `processId` (int): 进程ID
  - `dialogName` (string): 对话框名称
- **响应**:
```json
{
  "success": true,
  "message": "关闭成功"
}
```

### 15.13 移动对话框
- **URL**: `POST /api/window/moveDialog/{processId}/{x}/{y}?dialogName={dialogName}`
- **参数**:
  - `processId` (int): 进程ID
  - `x` (int): X坐标
  - `y` (int): Y坐标
  - `dialogName` (string): 对话框名称
- **响应**:
```json
{
  "success": true,
  "message": "对话框移动成功"
}
```

### 15.14 检查对话框是否存在
- **URL**: `GET /api/window/dialogExists/{processId}?dialogName={dialogName}`
- **参数**:
  - `processId` (int): 进程ID
  - `dialogName` (string): 对话框名称
- **响应**:
```json
{
  "success": true,
  "message": "对话框存在",
  "data": {
    "Exists": true
  }
}
```

### 15.15 根据标题检查窗口是否存在
- **URL**: `GET /api/window/windowExistsByTitle?title={title}`
- **参数**:
  - `title` (string): 窗口标题
- **响应**:
```json
{
  "success": true,
  "message": "窗口存在",
  "data": {
    "Exists": true
  }
}
```

### 15.16 截取窗口（裁剪方式）
- **URL**: `POST /api/window/captureByTailor/{processId}?savePath={savePath}`
- **参数**:
  - `processId` (int): 进程ID
  - `savePath` (string): 保存路径（必须是.png格式）
- **响应**:
```json
{
  "success": true,
  "message": "截图成功"
}
```

### 15.17 截取窗口（句柄方式）
- **URL**: `POST /api/window/captureByHandle/{processId}?savePath={savePath}`
- **参数**:
  - `processId` (int): 进程ID
  - `savePath` (string): 保存路径（必须是.png格式）
- **响应**:
```json
{
  "success": true,
  "message": "截图成功"
}
```

## 错误响应示例
```json
{
  "success": false,
  "message": "按钮不可用",
  "data": null,
  "errorCode": "BUTTON_DISABLED"
}
```

## 使用示例

### JavaScript调用示例
```javascript
// 检查按钮状态
fetch('http://localhost:9000/api/button/1234/btnSubmit/isEnabled')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('按钮可用:', data.data.IsEnabled);
    }
  });

// 模拟按键
fetch('http://localhost:9000/api/keyboard/pressKey/Enter', {
  method: 'POST'
})
  .then(response => response.json())
  .then(data => console.log(data.message));

// 设置编辑框文本
fetch('http://localhost:9000/api/edit/1234/txtInput/setText?text=Hello', {
  method: 'POST'
})
  .then(response => response.json())
  .then(data => console.log(data.message));
```

### C# 调用示例
```csharp
using (var client = new HttpClient())
{
    // 获取按钮状态
    var response = await client.GetAsync("http://localhost:9000/api/button/1234/btnSubmit/isEnabled");
    var json = await response.Content.ReadAsStringAsync();
    var result = JsonConvert.DeserializeObject<ResponseResult<object>>(json);

    // 点击按钮
    var clickResponse = await client.PostAsync("http://localhost:9000/api/button/1234/btnSubmit/click", null);
    var clickResult = await clickResponse.Content.ReadAsStringAsync();
}
```

### Python调用示例
```python
import requests

# 检查按钮状态
response = requests.get('http://localhost:9000/api/button/1234/btnSubmit/isEnabled')
data = response.json()
if data['success']:
    print(f"按钮可用: {data['data']['IsEnabled']}")

# 设置编辑框文本
response = requests.post('http://localhost:9000/api/edit/1234/txtInput/setText',
                        params={'text': 'Hello World'})
print(response.json()['message'])
```

## 接口总结

本API提供了完整的桌面应用程序自动化操作功能，包括：

- **UI控件操作**: 按钮、编辑框、复选框、单选按钮、下拉框、静态文本等
- **窗口管理**: 窗口大小调整、位置移动、焦点控制、截图等
- **系统交互**: 键盘模拟、串口通信、磁盘操作等
- **进程管理**: 进程查询、启动、终止等
- **元素检测**: UI元素存在性检查等

总计**16个功能模块**，**70+个API接口**，支持完整的桌面RPA自动化场景。