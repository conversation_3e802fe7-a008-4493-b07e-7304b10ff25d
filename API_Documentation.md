# 桌面RPA API接口文档

## 基础信息
- **Base URL**: `http://localhost:9000`
- **Content-Type**: `application/json`
- **响应格式**: JSON
- **框架**: ASP.NET Web API + OWIN自托管
- **支持CORS**: 是

## 通用响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "errorCode": null
}
```

## 0. 测试接口 (Hello)

### 0.1 健康检查
- **URL**: `GET /api/hello`
- **功能**: 测试API服务是否正常运行
- **响应**:
```json
{
  "Name": "Success",
  "Message": "Hello"
}
```

## 1. 按钮操作 (Button)

### 1.1 检查按钮是否可用
- **URL**: `GET /api/button/{ProcessId}/{AutomationId}/isEnabled`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnSubmit",
    "IsEnabled": true
  }
}
```

### 1.2 获取按钮文本
- **URL**: `GET /api/button/{ProcessId}/{AutomationId}/getText`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnSubmit",
    "ButtonText": "提交"
  }
}
```

### 1.3 单击按钮
- **URL**: `POST /api/button/{ProcessId}/{AutomationId}/click`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "单击成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnSubmit"
  }
}
```

### 1.4 异步单击按钮
- **URL**: `POST /api/button/{ProcessId}/{AutomationId}/clickAsync`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "异步单击成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnSubmit"
  }
}
```

### 1.5 双击按钮
- **URL**: `POST /api/button/{ProcessId}/{AutomationId}/doubleClick`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "双击成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "btnSubmit"
  }
}
```

## 2. 编辑框操作 (Edit)

### 2.1 获取编辑框文本
- **URL**: `GET /api/edit/{ProcessId}/{AutomationId}/getText`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "txtInput",
    "EditText": "输入的文本内容"
  }
}
```

## 3. 复选框操作 (CheckBox)

### 3.1 获取复选框文本
- **URL**: `GET /api/checkbox/{ProcessId}/{AutomationId}/getText`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "chkAgree",
    "CheckBoxText": "我同意条款"
  }
}
```

### 3.2 检查复选框是否可用
- **URL**: `GET /api/checkbox/{ProcessId}/{AutomationId}/isEnabled`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "chkAgree",
    "IsEnabled": true
  }
}
```

### 3.3 点击复选框（切换状态）
- **URL**: `POST /api/checkbox/{ProcessId}/{AutomationId}/click`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "复选框状态已切换"
}
```

### 3.4 获取复选框勾选状态
- **URL**: `GET /api/checkbox/{ProcessId}/{AutomationId}/isChecked`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "chkAgree",
    "IsChecked": true
  }
}
```

### 3.5 勾选复选框
- **URL**: `POST /api/checkbox/{ProcessId}/{AutomationId}/check`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "复选框已勾选"
}
```

### 3.6 取消勾选复选框
- **URL**: `POST /api/checkbox/{ProcessId}/{AutomationId}/uncheck`
- **参数**:
  - `ProcessId` (int): 进程ID
  - `AutomationId` (string): 自动化元素ID
- **响应**:
```json
{
  "success": true,
  "message": "复选框已取消勾选"
}
```

## 4. 下拉框操作 (ComboBox)

### 4.1 获取所有选项值
- **URL**: `GET /api/comboBox/{ProcessId}/{AutomationId}/getValues`
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "cmbCity",
    "AllValues": ["北京", "上海", "广州", "深圳"]
  }
}
```

## 5. 单选按钮操作 (RadioButton)

### 5.1 获取单选按钮文本
- **URL**: `GET /api/radioButton/{ProcessId}/{AutomationId}/getText`
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "rbMale",
    "RadioButtonText": "男"
  }
}
```

## 6. 静态文本操作 (Static)

### 6.1 获取静态文本内容
- **URL**: `GET /api/static/{ProcessId}/{AutomationId}/getText`
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "AutomationId": "lblTitle",
    "StaticText": "欢迎使用系统"
  }
}
```

## 7. 菜单操作 (Menu)

### 7.1 获取顶层菜单
- **URL**: `GET /api/menu/{ProcessId}/getTopMenus`
- **响应**:
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "ProcessId": 1234,
    "TopMenus": ["文件", "编辑", "视图", "帮助"]
  }
}
```

## 8. 键盘操作 (Keyboard)

### 8.1 模拟按键
- **URL**: `POST /api/keyboard/pressKey/{key}`
- **参数**:
  - `key` (string): 按键名称 (如: "Enter", "Tab", "Ctrl+C")
- **响应**:
```json
{
  "success": true,
  "message": "按键 Enter 模拟成功"
}
```

## 9. 串口操作 (SerialPort)

### 9.1 获取所有串口号
- **URL**: `GET /api/serialPort/getPortNames`
- **响应**:
```json
{
  "success": true,
  "message": "获取所有串口号成功",
  "data": {
    "PortNames": ["COM1", "COM2", "COM3"]
  }
}
```

## 错误响应示例
```json
{
  "success": false,
  "message": "按钮不可用",
  "data": null,
  "errorCode": "BUTTON_DISABLED"
}
```

## 使用示例

### JavaScript调用示例
```javascript
// 检查按钮状态
fetch('http://localhost:9000/api/button/1234/btnSubmit/isEnabled')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('按钮可用:', data.data.IsEnabled);
    }
  });

// 模拟按键
fetch('http://localhost:9000/api/keyboard/pressKey/Enter', {
  method: 'POST'
})
  .then(response => response.json())
  .then(data => console.log(data.message));
```

### C# 调用示例
```csharp
using (var client = new HttpClient())
{
    var response = await client.GetAsync("http://localhost:9000/api/button/1234/btnSubmit/isEnabled");
    var json = await response.Content.ReadAsStringAsync();
    var result = JsonConvert.DeserializeObject<ResponseResult<object>>(json);
}
```