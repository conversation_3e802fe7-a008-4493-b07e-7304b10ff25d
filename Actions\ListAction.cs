﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;
using System.Windows.Automation;


namespace DesktopRpaLib.Actions
{
    public class ListAction
    {


        // 选择指定的 ListViewItem
        public void SelectListViewItemByName(int processId, string automationId, string targetName)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            // 查找并选择特定的 List 项目
            var listItem = ActionUtil.FindElementByName(element, targetName);
            if (listItem != null)
            {
                // 使用 SelectionItemPattern 来选择 List 项目
                if (listItem.TryGetCurrentPattern(SelectionItemPattern.Pattern, out var patternObj))
                {
                    var selectionItemPattern = patternObj as SelectionItemPattern;
                    selectionItemPattern.Select();
                }
                else
                {
                    throw new Exception("无法选择 ListViewItem");
                }
            }
            else
            {
                throw new Exception("未找到匹配的 ListViewItem");
            }
        }



        // 获取ListView下所有ListViewItem的Name
        public List<string> GetListViewItemNames(int processId, string automationId)
        {
            var listNames = new List<string>();

            try
            {
                // 获取ListView元素
                var element = ActionUtil.GetElement(processId, automationId);

                // 获取所有ListViewItem
                var listItems = element.FindAll(TreeScope.Children,
                    new PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.ListItem));

                // 遍历所有ListViewItem并获取Name
                foreach (AutomationElement item in listItems)
                {
                    string name = item.Current.Name;
                    if (!string.IsNullOrEmpty(name))
                    {
                        listNames.Add(name);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取ListViewItem失败: {ex.Message}");
            }

            return listNames;
        }



    }
}
