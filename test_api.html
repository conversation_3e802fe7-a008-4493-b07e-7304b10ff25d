<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桌面RPA API测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        input, select {
            padding: 5px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .input-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>桌面RPA API测试工具</h1>
        <p>API服务地址: <strong>http://localhost:9000</strong></p>
        
        <!-- 基础测试 -->
        <div class="test-section">
            <h2>1. 基础测试</h2>
            <button onclick="testHello()">健康检查</button>
            <button onclick="testSerialPorts()">获取串口列表</button>
            <button onclick="testRemovableDrives()">获取可移动驱动器</button>
            <div id="basic-result" class="result"></div>
        </div>

        <!-- 键盘操作测试 -->
        <div class="test-section">
            <h2>2. 键盘操作测试</h2>
            <div class="input-group">
                <label>按键:</label>
                <select id="keySelect">
                    <option value="enter">Enter</option>
                    <option value="tab">Tab</option>
                    <option value="up">Up</option>
                    <option value="down">Down</option>
                    <option value="left">Left</option>
                    <option value="right">Right</option>
                </select>
                <button onclick="testKeyboard()">模拟按键</button>
            </div>
            <div id="keyboard-result" class="result"></div>
        </div>

        <!-- 进程操作测试 -->
        <div class="test-section">
            <h2>3. 进程操作测试</h2>
            <div class="input-group">
                <label>进程名:</label>
                <input type="text" id="exeName" placeholder="例如: notepad" value="notepad">
                <button onclick="testGetPids()">获取PID列表</button>
            </div>
            <div class="input-group">
                <label>进程ID:</label>
                <input type="number" id="processId" placeholder="例如: 1234">
                <button onclick="testGetExeInfo()">获取进程信息</button>
            </div>
            <div id="process-result" class="result"></div>
        </div>

        <!-- 窗口操作测试 -->
        <div class="test-section">
            <h2>4. 窗口操作测试</h2>
            <div class="input-group">
                <label>进程ID:</label>
                <input type="number" id="windowProcessId" placeholder="例如: 1234">
                <button onclick="testGetWindowInfo()">获取窗口信息</button>
                <button onclick="testFocusWindow()">窗口获取焦点</button>
            </div>
            <div class="input-group">
                <label>窗口标题:</label>
                <input type="text" id="windowTitle" placeholder="例如: 记事本">
                <button onclick="testWindowExists()">检查窗口是否存在</button>
            </div>
            <div id="window-result" class="result"></div>
        </div>

        <!-- UI控件测试 -->
        <div class="test-section">
            <h2>5. UI控件测试</h2>
            <div class="input-group">
                <label>进程ID:</label>
                <input type="number" id="uiProcessId" placeholder="例如: 1234">
            </div>
            <div class="input-group">
                <label>AutomationId:</label>
                <input type="text" id="automationId" placeholder="例如: btnOK">
                <button onclick="testButtonEnabled()">检查按钮可用性</button>
            </div>
            <div class="input-group">
                <label>元素名称:</label>
                <input type="text" id="elementName" placeholder="例如: 确定">
                <button onclick="testElementExists()">检查元素是否存在</button>
            </div>
            <div id="ui-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:9000';

        // 通用请求函数
        async function makeRequest(url, method = 'GET', resultElementId) {
            const resultElement = document.getElementById(resultElementId);
            resultElement.textContent = '请求中...';
            resultElement.className = 'result';

            try {
                const response = await fetch(url, { method });
                const data = await response.json();
                
                resultElement.textContent = JSON.stringify(data, null, 2);
                resultElement.className = data.success ? 'result success' : 'result error';
            } catch (error) {
                resultElement.textContent = `错误: ${error.message}`;
                resultElement.className = 'result error';
            }
        }

        // 基础测试函数
        function testHello() {
            makeRequest(`${API_BASE}/api/hello`, 'GET', 'basic-result');
        }

        function testSerialPorts() {
            makeRequest(`${API_BASE}/api/serialPort/getPortNames`, 'GET', 'basic-result');
        }

        function testRemovableDrives() {
            makeRequest(`${API_BASE}/api/disk/getRemovableDrives`, 'GET', 'basic-result');
        }

        // 键盘测试
        function testKeyboard() {
            const key = document.getElementById('keySelect').value;
            makeRequest(`${API_BASE}/api/keyboard/pressKey/${key}`, 'POST', 'keyboard-result');
        }

        // 进程测试
        function testGetPids() {
            const exeName = document.getElementById('exeName').value;
            if (!exeName) {
                alert('请输入进程名');
                return;
            }
            makeRequest(`${API_BASE}/api/exe/getPidsByExeName?exeName=${exeName}`, 'GET', 'process-result');
        }

        function testGetExeInfo() {
            const processId = document.getElementById('processId').value;
            if (!processId) {
                alert('请输入进程ID');
                return;
            }
            makeRequest(`${API_BASE}/api/exe/getExeNameAndTitle/${processId}`, 'GET', 'process-result');
        }

        // 窗口测试
        function testGetWindowInfo() {
            const processId = document.getElementById('windowProcessId').value;
            if (!processId) {
                alert('请输入进程ID');
                return;
            }
            makeRequest(`${API_BASE}/api/window/sizePos/${processId}`, 'GET', 'window-result');
        }

        function testFocusWindow() {
            const processId = document.getElementById('windowProcessId').value;
            if (!processId) {
                alert('请输入进程ID');
                return;
            }
            makeRequest(`${API_BASE}/api/window/focus/${processId}`, 'POST', 'window-result');
        }

        function testWindowExists() {
            const title = document.getElementById('windowTitle').value;
            if (!title) {
                alert('请输入窗口标题');
                return;
            }
            makeRequest(`${API_BASE}/api/window/windowExistsByTitle?title=${encodeURIComponent(title)}`, 'GET', 'window-result');
        }

        // UI控件测试
        function testButtonEnabled() {
            const processId = document.getElementById('uiProcessId').value;
            const automationId = document.getElementById('automationId').value;
            if (!processId || !automationId) {
                alert('请输入进程ID和AutomationId');
                return;
            }
            makeRequest(`${API_BASE}/api/button/${processId}/${automationId}/isEnabled`, 'GET', 'ui-result');
        }

        function testElementExists() {
            const processId = document.getElementById('uiProcessId').value;
            const elementName = document.getElementById('elementName').value;
            if (!processId || !elementName) {
                alert('请输入进程ID和元素名称');
                return;
            }
            makeRequest(`${API_BASE}/api/element/${processId}/isExistByName?elementName=${encodeURIComponent(elementName)}`, 'GET', 'ui-result');
        }

        // 页面加载完成后自动测试健康检查
        window.onload = function() {
            testHello();
        };
    </script>
</body>
</html>
