﻿using System.Diagnostics;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

public class LoggingHandler : DelegatingHandler
{
    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        // 记录请求信息
        Trace.WriteLine($"[Request] Method: {request.Method}, URL: {request.RequestUri}");

        // 调用内部处理器，获取响应
        var response = await base.SendAsync(request, cancellationToken);

        // 记录响应信息
        Trace.WriteLine($"[Response] Status Code: {response.StatusCode}");

        // 记录响应数据（JSON）
        if (response.Content != null)
        {
            var responseData = await response.Content.ReadAsStringAsync();
            Trace.WriteLine($"[Response] Data: {responseData}");
        }

        return response;
    }
}
