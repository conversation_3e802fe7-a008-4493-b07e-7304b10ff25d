using System; // 导入System命名空间，用于基本的系统功能
using Microsoft.Owin.Hosting; // 导入Microsoft.Owin.Hosting命名空间，用于启动和停止OWIN应用程序

class Program
{
    static void Main(string[] args)
    {
        string baseAddress = "http://localhost:9000/"; // 定义应用程序的基础地址

        try
        {
            // Start OWIN host 
            using (WebApp.Start<Startup>(url: baseAddress)) // 使用WebApp.Start方法启动OWIN主机，传入基础地址和启动类
            {
                Console.WriteLine("Server running on {0}", baseAddress); // 输出服务器运行的地址
                Console.ReadLine(); // 读取控制台输入，防止程序立即退出
            }
        }
        catch (Exception ex)
        {
            // 捕获任何异常并输出错误信息
            Console.WriteLine("Error occurred while starting the server: " + ex.Message);
        }

        // 即使发生异常，程序也不会立即终止
        Console.WriteLine("Program has exited.");
        Console.ReadLine(); // 防止程序退出
    }
}
