﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="FlaUI.Core" version="3.2.0" targetFramework="net472" />
  <package id="FlaUI.UIA3" version="3.2.0" targetFramework="net472" />
  <package id="Interop.UIAutomationClient" version="10.18362.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.Cors" version="5.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="6.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Owin" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.3.0" targetFramework="net472" />
  <package id="Microsoft.Owin" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Cors" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Host.HttpListener" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Hosting" version="4.2.2" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="Newtonsoft.Json.Bson" version="1.0.2" targetFramework="net472" />
  <package id="Owin" version="1.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Drawing.Common" version="9.0.0" targetFramework="net472" />
  <package id="System.Management" version="4.7.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.5.3" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
</packages>