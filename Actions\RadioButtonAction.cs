﻿using System;
using System.Diagnostics;
using System.Windows.Automation;

namespace DesktopRpaLib.Actions
{
    class RadioButtonAction
    {



        // 单选按钮是否可用
        public bool IsRadioButtonEnabled(int processId, string automationId)
        {
            return ActionUtil.IsElementEnabled(processId, automationId);
        }





        // 获取单选按钮的文本内容
        public string GetRadioButtonText(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            var radioButtonText = element.GetCurrentPropertyValue(AutomationElement.NameProperty)?.ToString();
            if (string.IsNullOrEmpty(radioButtonText))
            {
                throw new Exception("单选按钮文本为空或未设置");
            }

            return radioButtonText;
        }




        // 点击单选按钮
        public void ClickRadioButton(int processId, string automationId)
        {
            var element = ActionUtil.GetElement(processId, automationId);

            // 使用 SelectionItemPattern 来选择单选按钮
            if (element.TryGetCurrentPattern(SelectionItemPattern.Pattern, out object pattern))
            {
                var selectionItemPattern = (SelectionItemPattern)pattern;
                selectionItemPattern.Select();
            }
            else
            {
                throw new Exception("无法获取单选按钮的 SelectionItemPattern");
            }
        }



    }
}
