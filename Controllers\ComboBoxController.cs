﻿// HelloWorldController.cs
using System.Web.Http;
using DesktopRpaLib.Response;
using DesktopRpaLib.Actions;
using System;



// 下拉框接口

[RoutePrefix("api/comboBox/{ProcessId}/{AutomationId}")]
public class ComboBoxController : ApiController // 继承自ApiController，定义一个Web API控制器
{

    private readonly ComboBoxAction _comboBoxAction = new ComboBoxAction();




    // 获取所有值
    [Route("getValues")]
    public IHttpActionResult GetValues(int ProcessId, string AutomationId)
    {


        try
        {
            var allValues = _comboBoxAction.GetComboBoxItems(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, AllValues = allValues };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }



    }



    // 获取当前值
    [Route("getCurrentValue")]
    public IHttpActionResult GetCurrentValue(int ProcessId, string AutomationId)
    {

        try
        {
            var currentValue = _comboBoxAction.GetCurrentComboBoxValue(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, CurrentValue = currentValue };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 选择指定值
    [Route("select")]
    public IHttpActionResult PostSelectVal(int ProcessId, string AutomationId, [FromUri] string value)
    {

        try
        {

            if (string.IsNullOrEmpty(value))
            {
                return Json(ResponseResult<string>.Error(msg: "value参数不能为空"));
            }


            _comboBoxAction.SetComboBoxValue(ProcessId, AutomationId, value);
            return Json(ResponseResult<object>.Success(msg: "选择成功"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }

    }
}


