﻿using System;
using System.IO.Ports;
using System.Threading;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Text;
using System.IO;
using System.Linq;



namespace DesktopRpaLib.Actions
{


    // 定义与C语言结构体对应的DiskInfo结构体
    [StructLayout(LayoutKind.Sequential, Pack = 4)]
    public struct DiskInfo
    {
        public uint disk_location;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 200)]
        public byte[] main_control;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 200)]
        public byte[] disk_read_and_write_capacity;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 200)]
        public byte[] flash_name;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 200)]
        public byte[] disk_name;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 200)]
        public byte[] bp_mode;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 200)]
        public byte[] plane_mode;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 200)]
        public byte[] speed_flag;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 200)]
        public byte[] speed_code;
    }



    // DllImport类，用来加载并调用DLL函数
    public class DllUtil
    {
        [DllImport("FlashSpeedSta.dll", CallingConvention = CallingConvention.StdCall)]
        public static extern void InitDiskData(string iniPath);

        [DllImport("FlashSpeedSta.dll", CallingConvention = CallingConvention.StdCall)]
        public static extern void GetDiskNum(ref int diskCount);

        [DllImport("FlashSpeedSta.dll", CallingConvention = CallingConvention.StdCall)]
        public static extern bool RefreshDisk([In, Out] DiskInfo[] diskInfo, bool notDiskNameMode);

        [DllImport("FlashSpeedSta.dll", CallingConvention = CallingConvention.StdCall)]
        public static extern void RefreshHUB([In, Out] DiskInfo[] diskInfo, bool notDiskNameMode);

        [DllImport("FlashSpeedSta.dll", CallingConvention = CallingConvention.StdCall)]
        public static extern void SaveHUB();
    }






    class SerialPortAction
    {
        // 定义命令常量
        private const byte CMD_LED_STATUS = 0x01;
        private const byte CMD_DCDC = 0x02;
        private const byte CMD_START_LIGHTS = 0x03;
        private const byte CMD_STOP_LIGHTS = 0x04;
        private const byte CMD_LED_ON = 0x05;
        private const byte CMD_LED_OFF = 0x06;



        // 发送命令
        public bool SendCommand(byte cmd, byte port, byte data, string comPort)
        {
            try
            {
                // 打开串口
                using (SerialPort serialPort = new SerialPort(comPort, 9600))
                {
                    serialPort.Open();

                    // 构建命令
                    byte code = (byte)new Random().Next(0, 256);  // 随机生成 0-255 之间的数字
                    byte[] command = new byte[] { 0x54, cmd, port, data, code };

                    // 发送数据
                    serialPort.Write(command, 0, command.Length);
                    Thread.Sleep(200);  // 延时0.2秒，确保命令发送完整

                    // 读取响应
                    byte[] response = new byte[5];
                    serialPort.Read(response, 0, 5);

                    // 解析响应
                    string flag = response[3].ToString("X");
                    string code2 = response[4].ToString("X");

                    // 判断响应
                    if (code.ToString("X") == code2)
                    {
                        return flag == "1";  // 如果响应的 flag 为 1，返回 true
                    }
                    else
                    {
                        return false;  // 如果响应不正确，返回 false
                    }
                }
            }
            catch (Exception ex)
            {
                string msg;

                if (data == 0)
                {
                    msg = $"断电命令执行错误: {ex.Message}";
                }
                else if (data == 1)
                {
                    msg = $"上电命令执行错误: {ex.Message}";
                }
                else
                {
                    msg = $"命令执行错误: {ex.Message}";
                }
                throw new Exception(msg);

            }
        }


        // 获取所有串口号
        public string[] GetPortNames()
        {
            return SerialPort.GetPortNames();
        }


        // 断电
        public bool PowerOff(string comPort, int portNumber)
        {
            byte port = (byte)(portNumber - 1);

            return SendCommand(CMD_DCDC, port, 0, comPort);
        }


        // 上电
        public bool PowerOn(string comPort, int portNumber)
        {
            byte port = (byte)(portNumber - 1);

            return SendCommand(CMD_DCDC, port, 1, comPort);
        }





        // 获取磁盘信息
        public List<Dictionary<string, string>> CallDll()
        {
            List<Dictionary<string, string>> dllInfoList = new List<Dictionary<string, string>>();

            try
            {
                string dllPath = AppDomain.CurrentDomain.BaseDirectory;

                // 初始化
                DllUtil.InitDiskData(dllPath);

                // 获取磁盘数量
                int diskCount = 0;
                DllUtil.GetDiskNum(ref diskCount);

                if (diskCount > 0)
                {
                    // 创建磁盘信息数组
                    DiskInfo[] diskInfos = new DiskInfo[diskCount];
                    bool notDiskNameMode = false;

                    // 获取磁盘信息
                    if (DllUtil.RefreshDisk(diskInfos, notDiskNameMode))
                    {
                        foreach (var diskInfo in diskInfos)
                        {
                            try
                            {

                                // 处理字节数组，去除 0xFD 填充
                                string GetString(byte[] bytes)
                                {
                                    int length = 0;
                                    for (int i = 0; i < bytes.Length; i++)
                                    {
                                        if (bytes[i] == 0x00 || bytes[i] == 0xFD)
                                        {
                                            length = i;
                                            break;
                                        }
                                    }
                                    if (length == 0) return string.Empty;
                                    return Encoding.ASCII.GetString(bytes, 0, length);
                                }


                                // 特殊处理disk_name
                                string GetDiskName(byte[] bytes)
                                {
                                    string fullPath = GetString(bytes);
                                    if (fullPath.Contains("\\"))
                                    {
                                        // 提取盘符，例如从 "\\.\E:" 中提取 "E"
                                        return fullPath.Split('\\').Last().Replace(":", "");
                                    }
                                    return string.Empty;
                                }

                                var diskInfoDict = new Dictionary<string, string>
                                {
                                    { "DiskLocation", (diskInfo.disk_location + 1).ToString() },
                                    { "FlashName", GetString(diskInfo.flash_name) },
                                    { "SpeedCode", GetString(diskInfo.speed_code) },
                                    { "DiskName", GetDiskName(diskInfo.disk_name) }
                                };

                                dllInfoList.Add(diskInfoDict);

                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"处理单个磁盘信息时出错: {ex.Message}");
                                continue;
                            }
                        }
                    }
                    else
                    {
                        throw new Exception("获取Hub信息失败：RefreshDisk 返回 false");
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取磁盘信息失败: {ex.Message}");
            }

            return dllInfoList;
        }



        // 获取Hub信息
        public List<Dictionary<string, string>> GetHubInfo()
        {
            var hubInfo = CallDll();
            return hubInfo;
        }







    }
}
