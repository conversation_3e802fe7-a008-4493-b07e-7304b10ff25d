﻿using System.Web.Http;
using DesktopRpaLib.Actions;
using DesktopRpaLib.Response;
using System;

[RoutePrefix("api/edit/{ProcessId}/{AutomationId}")] // 定义路由前缀
public class EditController : ApiController
{
    private readonly EditAction _editAction = new EditAction();




    // 获取编辑框文本内容
    [Route("getText")]
    public IHttpActionResult GetEditText(int ProcessId, string AutomationId)
    {
        try
        {
            var text = _editAction.GetEditText(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, EditText = text };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 设置编辑框文本内容
    [Route("setText")]
    public IHttpActionResult PostSetEditText(int ProcessId, string AutomationId, [FromUri] string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return Json(ResponseResult<string>.Error(msg: "text参数不能为空"));
        }


        try
        {
            _editAction.SetEditText(ProcessId, AutomationId, text);
            return Json(ResponseResult<object>.Success(msg: "文本已设置"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }




    // 清空编辑框文本内容
    [Route("clearText")]
    public IHttpActionResult PostClearEditText(int ProcessId, string AutomationId)
    {
        try
        {
            _editAction.ClearEditText(ProcessId, AutomationId);
            return Json(ResponseResult<object>.Success(msg: "文本已清空"));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }


    // 判断编辑框是否可用
    [Route("isEnabled")]
    public IHttpActionResult GetIsEditEnabled(int ProcessId, string AutomationId)
    {
        try
        {
            var isEnabled = _editAction.IsEditEnabled(ProcessId, AutomationId);
            var data = new { ProcessId, AutomationId, IsEnabled = isEnabled };
            return Json(ResponseResult<object>.Success(msg: "查询成功", data: data));
        }
        catch (Exception ex)
        {
            return Json(ResponseResult<string>.Error(msg: ex.Message));
        }
    }
}
