﻿using System;
using System.Diagnostics;
using System.Windows.Automation;
using System.Threading;
using System.Threading.Tasks;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using FlaUI.Core;
using FlaUI.UIA3;
using System.Runtime.InteropServices;
using System.Drawing;
using System.Windows.Forms;
using System.Drawing.Imaging;



namespace DesktopRpaLib.Actions
{
    public class WindowAction
    {


        // 窗口状态常量
        private const int SW_RESTORE = 9;
        private const int SW_MINIMIZE = 6;
        private const int SW_MAXIMIZE = 3;

        // SetWindowPos 常量
        private const uint TOPMOST_FLAGS = 0x0001 | 0x0002;
        private static readonly IntPtr HWND_TOPMOST = new IntPtr(-1);
        private static readonly IntPtr HWND_NOTOPMOST = new IntPtr(-2);

        private const uint MOUSEEVENTF_LEFTDOWN = 0x0002; // 鼠标左键按下
        private const uint MOUSEEVENTF_LEFTUP = 0x0004;   // 鼠标左键抬起


        // 添加 RECT 结构体定义
        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }


        // 需要在类的顶部添加这些 DllImport
        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int nCmdShow);

        [DllImport("user32.dll")]
        private static extern bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int x, int y, int cx, int cy, uint uFlags);

        [DllImport("user32.dll")]
        private static extern IntPtr GetForegroundWindow();

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        private static extern void mouse_event(uint dwFlags, uint dx, uint dy, uint dwData, int dwExtraInfo);


        // DllImport 用于发送关闭消息
        [DllImport("user32.dll")]
        private static extern IntPtr SendMessage(IntPtr hWnd, uint Msg, IntPtr wParam, IntPtr lParam);

        // 消息常量
        private const uint WM_CLOSE = 0x0010;


        // 窗口信息结构体
        private struct WindowInfo
        {
            public IntPtr Handle;
            public string Title;
            public int ProcessId;
        }




        [DllImport("user32.dll")]
        private static extern bool PrintWindow(IntPtr hwnd, IntPtr hdcBlt, uint nFlags);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleDC(IntPtr hdc);

        [DllImport("gdi32.dll")]
        private static extern IntPtr CreateCompatibleBitmap(IntPtr hdc, int width, int height);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteDC(IntPtr hdc);

        [DllImport("gdi32.dll")]
        private static extern bool DeleteObject(IntPtr hObject);

        [DllImport("user32.dll")]
        private static extern IntPtr GetDC(IntPtr hwnd);

        [DllImport("user32.dll")]
        private static extern bool ReleaseDC(IntPtr hwnd, IntPtr dc);




        // 获取窗口状态栏的文本
        public string GetStatusBarText(int processId)
        {
            try
            {
                // 获取指定进程的窗口句柄
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle == IntPtr.Zero)
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }

                // 使用 UIAutomation 查找窗口元素
                AutomationElement rootElement = AutomationElement.FromHandle(mainWindowHandle);
                if (rootElement == null)
                {
                    throw new Exception("无法找到根元素");
                }

                // 查找所有状态栏控件
                // 使用 ControlType 属性查找状态栏
                var statusBars = rootElement.FindAll(TreeScope.Descendants,
                    new PropertyCondition(AutomationElement.ControlTypeProperty, ControlType.StatusBar));

                if (statusBars == null || statusBars.Count == 0)
                {
                    throw new Exception("未找到状态栏控件");
                }

                // 获取第一个状态栏
                var statusBar = statusBars[0];
                string statusText = "";

                // 尝试获取状态栏的所有子项
                var statusItems = statusBar.FindAll(TreeScope.Children, Condition.TrueCondition);
                if (statusItems != null && statusItems.Count > 0)
                {
                    // 拼接所有子项的文本
                    foreach (AutomationElement item in statusItems)
                    {
                        string itemText = "";

                        // 尝试通过 ValuePattern 获取文本
                        if (item.TryGetCurrentPattern(ValuePattern.Pattern, out var valuePatternObj))
                        {
                            var valuePattern = valuePatternObj as ValuePattern;
                            if (valuePattern != null)
                            {
                                itemText = valuePattern.Current.Value;
                            }
                        }
                        // 如果获取不到 Value，则尝试获取 Name 属性
                        if (string.IsNullOrEmpty(itemText))
                        {
                            itemText = item.Current.Name;
                        }

                        if (!string.IsNullOrEmpty(itemText))
                        {
                            statusText += (statusText.Length > 0 ? " " : "") + itemText;
                        }
                    }
                }
                else
                {
                    // 如果没有子项，直接获取状态栏的文本
                    statusText = statusBar.Current.Name;
                }

                return string.IsNullOrEmpty(statusText) ? "状态栏无文本" : statusText;
            }
            catch (Exception ex)
            {
                throw new Exception($"获取状态栏文本失败: {ex.Message}");
            }
        }



        // 设置窗口大小
        public void SetWindowSize(int processId, int width, int height)
        {
            try
            {
                // 获取指定进程的窗口句柄
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 获取当前窗口的矩形信息
                    RECT windowRect;
                    if (!GetWindowRect(mainWindowHandle, out windowRect))
                    {
                        throw new Exception("无法获取窗口矩形信息");
                    }

                    // 使用 SetWindowPos 设置窗口的新尺寸
                    if (!SetWindowPos(mainWindowHandle, IntPtr.Zero, windowRect.Left, windowRect.Top, width, height, 0x0004))
                    {
                        throw new Exception("设置窗口大小失败");
                    }
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"设置窗口大小失败: {ex.Message}");
            }
        }






        // 窗口恢复
        public void RestoreWindow(int processId)
        {
            try
            {
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    ShowWindow(mainWindowHandle, SW_RESTORE);
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"恢复窗口失败: {ex.Message}");
            }
        }





        // 窗口最小化
        public void MinimizeWindow(int processId)
        {
            try
            {
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    ShowWindow(mainWindowHandle, SW_MINIMIZE);
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"最小化窗口失败: {ex.Message}");
            }
        }





        // 窗口最大化
        public void MaximizeWindow(int processId)
        {
            try
            {
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    ShowWindow(mainWindowHandle, SW_MAXIMIZE);
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"最大化窗口失败: {ex.Message}");
            }
        }




        // 窗口置顶
        public void TopWindow(int processId)
        {
            try
            {
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 将窗口设置为置顶
                    SetWindowPos(mainWindowHandle, HWND_TOPMOST, 0, 0, 0, 0, TOPMOST_FLAGS);
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"窗口置顶失败: {ex.Message}");
            }
        }



        // 窗口获取焦点（临时置顶）
        public void FocusWindow(int processId)
        {
            try
            {
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 让窗口获得焦点并置于最前
                    SetForegroundWindow(mainWindowHandle);
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"窗口聚焦失败: {ex.Message}");
            }
        }





        // 移动窗口到指定xy坐标
        public void MoveWindow(int processId, int x, int y)
        {
            try
            {
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 获取当前窗口的矩形信息
                    RECT windowRect;
                    if (!GetWindowRect(mainWindowHandle, out windowRect))
                    {
                        throw new Exception("无法获取窗口矩形信息");
                    }

                    // 计算窗口的宽度和高度
                    int width = windowRect.Right - windowRect.Left;
                    int height = windowRect.Bottom - windowRect.Top;

                    // 移动窗口到指定坐标
                    if (!SetWindowPos(mainWindowHandle, IntPtr.Zero, x, y, width, height, 0x0004))
                    {
                        throw new Exception("移动窗口失败");
                    }
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"移动窗口失败: {ex.Message}");
            }
        }


        // 平滑移动鼠标到指定坐标
        private void SmoothMoveCursor(int targetX, int targetY)
        {
            var startX = Cursor.Position.X;
            var startY = Cursor.Position.Y;

            int steps = 20; // 移动的步数
            for (int i = 0; i <= steps; i++)
            {
                int newX = (int)(startX + (targetX - startX) * i / steps);
                int newY = (int)(startY + (targetY - startY) * i / steps);
                Cursor.Position = new System.Drawing.Point(newX, newY);
                Thread.Sleep(10); // 每步之间的延迟
            }
        }



        // 点击指定坐标
        public void ClickWindow(int processId, int x, int y)
        {
            try
            {
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 设置窗口为前景窗口
                    SetForegroundWindow(mainWindowHandle);

                    // 计算绝对坐标
                    RECT windowRect;
                    if (!GetWindowRect(mainWindowHandle, out windowRect))
                    {
                        throw new Exception("无法获取窗口矩形信息");
                    }

                    int absoluteX = windowRect.Left + x;
                    int absoluteY = windowRect.Top + y;

                    // 平滑移动鼠标到指定坐标
                    SmoothMoveCursor(absoluteX, absoluteY);


                    // 模拟鼠标点击
                    mouse_event(MOUSEEVENTF_LEFTDOWN, (uint)absoluteX, (uint)absoluteY, 0, 0);
                    mouse_event(MOUSEEVENTF_LEFTUP, (uint)absoluteX, (uint)absoluteY, 0, 0);
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"点击失败: {ex.Message}");
            }
        }





        // 获取窗口的宽度、高度和坐标（x, y）
        public (int x, int y, int width, int height) GetWindowSizePos(int processId)
        {
            try
            {
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 获取窗口的矩形信息
                    RECT windowRect;
                    if (GetWindowRect(mainWindowHandle, out windowRect))
                    {
                        int x = windowRect.Left;
                        int y = windowRect.Top;
                        int width = windowRect.Right - windowRect.Left;
                        int height = windowRect.Bottom - windowRect.Top;
                        return ( width, height, x, y);
                    }
                    else
                    {
                        throw new Exception("无法获取窗口矩形信息");
                    }
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取窗口信息失败: {ex.Message}");
            }
        }






        // 获取指定 processId 和名称的对话框的宽度、高度和坐标（x, y）
        public (int x, int y, int width, int height) GetDialogSizePos(int processId, string dialogName)
        {
            try
            {
                var process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 获取主窗口的 AutomationElement
                    AutomationElement mainWindowElement = AutomationElement.FromHandle(mainWindowHandle);

                    // 创建条件以查找对话框
                    var condition = new PropertyCondition(AutomationElement.NameProperty, dialogName);
                    var dialogElement = mainWindowElement.FindFirst(TreeScope.Descendants, condition);

                    if (dialogElement != null)
                    {
                        // 找到对话框
                        IntPtr dialogHandle = new IntPtr(dialogElement.Current.NativeWindowHandle);

                        // 获取窗口的矩形信息
                        RECT windowRect;
                        if (GetWindowRect(dialogHandle, out windowRect))
                        {
                            int width = windowRect.Right - windowRect.Left;
                            int height = windowRect.Bottom - windowRect.Top;
                            int x = windowRect.Left;
                            int y = windowRect.Top;

                            return (width, height, x, y);
                        }
                        else
                        {
                            throw new Exception("无法获取对话框的窗口矩形信息");
                        }
                    }
                    else
                    {
                        throw new Exception($"未找到名称为 '{dialogName}' 的对话框");
                    }
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取对话框信息失败: {ex.Message}");
            }
        }




        // 获取所有窗口句柄
        private List<WindowInfo> GetAllWindows()
        {
            var windowList = new List<WindowInfo>();
            foreach (Process p in Process.GetProcesses())
            {
                try
                {
                    if (p.MainWindowHandle != IntPtr.Zero)
                    {
                        windowList.Add(new WindowInfo
                        {
                            Handle = p.MainWindowHandle,
                            Title = p.MainWindowTitle,
                            ProcessId = p.Id
                        });
                    }
                }
                catch
                {
                    // 处理无法访问的进程
                }
            }
            return windowList;
        }




        // 关闭指定 processId 和名称的对话框
        public void CloseDialog(int processId, string dialogName)
        {
            try
            {
                var process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 获取主窗口的 AutomationElement
                    AutomationElement mainWindowElement = AutomationElement.FromHandle(mainWindowHandle);

                    // 创建条件以查找对话框
                    var condition = new PropertyCondition(AutomationElement.NameProperty, dialogName);
                    var dialogElement = mainWindowElement.FindFirst(TreeScope.Descendants, condition);

                    if (dialogElement != null)
                    {
                        // 找到对话框，发送关闭消息
                        IntPtr dialogHandle = new IntPtr(dialogElement.Current.NativeWindowHandle);
                        ShowWindow(dialogHandle, SW_RESTORE); // 恢复对话框（如果被最小化）
                        SetForegroundWindow(dialogHandle);    // 将对话框置为前景
                        SendMessage(dialogHandle, WM_CLOSE, IntPtr.Zero, IntPtr.Zero); // 发送关闭消息
                    }
                    else
                    {
                        throw new Exception($"未找到名称为 '{dialogName}' 的对话框");
                    }
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"关闭对话框失败: {ex.Message}");
            }
        }


        // 移动指定 processId 和名称的对话框到指定的 x, y 坐标
        public void MoveDialog(int processId, int x, int y, string dialogName)
        {
            try
            {
                var process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 获取主窗口的 AutomationElement
                    AutomationElement mainWindowElement = AutomationElement.FromHandle(mainWindowHandle);

                    // 创建条件以查找对话框
                    var condition = new PropertyCondition(AutomationElement.NameProperty, dialogName);
                    var dialogElement = mainWindowElement.FindFirst(TreeScope.Descendants, condition);

                    if (dialogElement != null)
                    {
                        // 找到对话框
                        IntPtr dialogHandle = new IntPtr(dialogElement.Current.NativeWindowHandle);

                        // 获取当前窗口位置和大小
                        RECT windowRect;
                        if (GetWindowRect(dialogHandle, out windowRect))
                        {
                            int width = windowRect.Right - windowRect.Left;
                            int height = windowRect.Bottom - windowRect.Top;

                            // 使用 SetWindowPos 来移动窗口
                            SetWindowPos(dialogHandle, HWND_TOPMOST, x, y, width, height, 0);
                        }
                        else
                        {
                            throw new Exception("无法获取对话框的窗口矩形信息");
                        }
                    }
                    else
                    {
                        throw new Exception($"未找到名称为 '{dialogName}' 的对话框");
                    }
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"移动对话框失败: {ex.Message}");
            }
        }





        // 判断对话框是否存在
        public bool DialogExists(int processId, string dialogName)
        {
            try
            {
                var process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 获取主窗口的 AutomationElement
                    AutomationElement mainWindowElement = AutomationElement.FromHandle(mainWindowHandle);

                    // 创建条件以查找对话框
                    var condition = new PropertyCondition(AutomationElement.NameProperty, dialogName);
                    var dialogElement = mainWindowElement.FindFirst(TreeScope.Descendants, condition);

                    return dialogElement != null; // 如果找到对话框，返回 true
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"检查对话框存在性失败: {ex.Message}");
            }
        }



        // 通过裁剪截取窗口，并保存到指定路径
        public void CaptureWindowByTailor(int processId, string savePath)
        {
            try
            {
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 确保窗口在屏幕内可见
                    RestoreWindow(processId);
                    FocusWindow(processId);
                    Thread.Sleep(500);

                    // 获取窗口矩形
                    RECT windowRect;
                    if (GetWindowRect(mainWindowHandle, out windowRect))
                    {
                        int width = windowRect.Right - windowRect.Left;
                        int height = windowRect.Bottom - windowRect.Top;

                        // 创建位图对象
                        using (Bitmap bitmap = new Bitmap(width, height))
                        {
                            using (Graphics graphics = Graphics.FromImage(bitmap))
                            {
                                // 从窗口中复制图像
                                graphics.CopyFromScreen(windowRect.Left, windowRect.Top, 0, 0, bitmap.Size);
                            }

                            // 保存图像到指定路径
                            bitmap.Save(savePath, ImageFormat.Png);
                        }
                    }
                    else
                    {
                        throw new Exception("无法获取窗口矩形信息");
                    }
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"截图失败: {ex.Message}");
            }

        }






        // 通过句柄截图
        public void CaptureWindowByHandle(int processId, string savePath)
        {
            try
            {
                Process process = Process.GetProcessById(processId);
                IntPtr mainWindowHandle = process.MainWindowHandle;

                if (mainWindowHandle != IntPtr.Zero)
                {
                    // 获取窗口矩形
                    RECT windowRect;
                    if (GetWindowRect(mainWindowHandle, out windowRect))
                    {
                        int width = windowRect.Right - windowRect.Left;
                        int height = windowRect.Bottom - windowRect.Top;

                        // 创建位图对象
                        using (Bitmap bitmap = new Bitmap(width, height))
                        {
                            using (Graphics graphics = Graphics.FromImage(bitmap))
                            {
                                IntPtr hdc = graphics.GetHdc();
                                IntPtr windowDC = GetDC(mainWindowHandle);

                                // 使用 PrintWindow 捕获窗口内容
                                PrintWindow(mainWindowHandle, hdc, 0);
                                graphics.ReleaseHdc(hdc);
                                ReleaseDC(mainWindowHandle, windowDC);
                            }

                            // 保存图像到指定路径
                            bitmap.Save(savePath, ImageFormat.Png);
                        }
                    }
                    else
                    {
                        throw new Exception("无法获取窗口矩形信息");
                    }
                }
                else
                {
                    throw new Exception($"无法获取进程 {processId} 的主窗口句柄");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"截图失败: {ex.Message}");
            }
        }



        // 根据窗口标题判断软件是否存在
        public bool WindowExistsByTitle(string windowTitle)
        {
            try
            {
                // 获取所有窗口
                var windows = GetAllWindows();

                // 遍历窗口列表，检查窗口标题是否匹配
                foreach (var window in windows)
                {
                    // 使用 IndexOf 来进行不区分大小写的比较
                    if (window.Title.IndexOf(windowTitle, StringComparison.OrdinalIgnoreCase) >= 0)
                    {
                        return true; // 找到窗口，返回 true
                    }
                }

                return false; // 未找到匹配的窗口
            }
            catch (Exception ex)
            {
                throw new Exception($"根据窗口标题判断软件是否存在失败: {ex.Message}");
            }
        }





    }

}
